package com.ssac.iot.data.listen;

import com.ssac.common.constant.FastBeeConstant;
import com.ssac.common.core.mq.DeviceTestReportBo;
import com.ssac.iot.data.consumer.DeviceTestConsumer;
import com.ssac.mq.queue.DeviceTestQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DeviceTestListen {

    @Resource
    private DeviceTestConsumer deviceTestConsumer;

    @Async(FastBeeConstant.TASK.DEVICE_TEST_TASK)
    public void listen(){
        while (true){
            try {
                DeviceTestReportBo take = DeviceTestQueue.take();

                deviceTestConsumer.consume(take);
            }catch (Exception e){
                log.error("=>emq数据转发异常");
            }
        }
    }
}
