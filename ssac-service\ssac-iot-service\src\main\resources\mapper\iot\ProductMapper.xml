<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.ProductMapper">

    <resultMap type="com.ssac.iot.domain.Product" id="ProductResult">
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="protocolCode" column="protocol_code"/>
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="tenantName"    column="tenant_name"    />
        <result property="isSys"    column="is_sys"    />
        <result property="isAuthorize"    column="is_authorize"    />
        <result property="mqttAccount"    column="mqtt_account"    />
        <result property="mqttPassword"    column="mqtt_password"    />
        <result property="mqttSecret"    column="mqtt_secret"    />
        <result property="status"    column="status"    />
        <result property="deviceType"    column="device_type"    />
        <result property="networkMethod"    column="network_method"    />
        <result property="vertificateMethod"    column="vertificate_method"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="transport" column="transport"/>
        <result property="locationWay"    column="location_way"    />
        <result property="isOwner"  column="is_owner"/>
        <result property="guid" column="guid"/>
    </resultMap>

    <resultMap type="com.ssac.iot.model.IdAndName" id="ProductShortResult">
        <result property="id"    column="product_id"    />
        <result property="name"    column="product_name"    />
    </resultMap>

    <sql id="selectProductVo">
        select product_id, product_name,protocol_code,transport, category_id, category_name, tenant_id, tenant_name, is_sys, is_authorize, mqtt_account,mqtt_password,mqtt_secret ,status, device_type, network_method, vertificate_method, create_time, update_time, img_url,remark,location_way,guid from iot_product
    </sql>

    <select id="selectProductList" parameterType="com.ssac.iot.domain.Product" resultMap="ProductResult">
        select p.product_id, p.product_name,p.protocol_code,p.transport, p.category_id,
        p.category_name, p.tenant_id, p.tenant_name, p.is_sys, p.is_authorize,
        p.mqtt_account,p.mqtt_password,p.mqtt_secret ,p.status,p.device_type,
        p.network_method, p.vertificate_method, p.create_time, p.update_time,
        p.img_url,p.remark,p.guid,
        case
        when (select count(product_id) from  iot_product where p.tenant_id = #{tenantId}) > 0  then  1
        else 0
        end as is_owner,
        p.location_way from iot_product p
        <where>
            <if test="deptId != null and showSenior and !isAdmin">
                and ( p.tenant_id = #{tenantId}
                    or (p.tenant_id in (
                            SELECT de.dept_user_id
                            FROM sys_dept de
                            WHERE FIND_IN_SET( de.dept_id,(
                            SELECT d.ancestors
                            FROM sys_dept d
                            WHERE d.dept_id = #{deptId} )
                            )
                    )
                    and p.is_sys = 0
                  )
                )
            </if>
            <if test="!showSenior and tenantId != null  and tenantId != 0 and !isAdmin">
                and p.tenant_id = #{tenantId}
            </if>
            <if test="productName != null  and productName != ''"> and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="categoryName != null  and categoryName != ''"> and p.category_name like concat('%', #{categoryName}, '%')</if>
            <if test="status != null "> and p.status = #{status}</if>
            <if test="deviceType != null "> and device_type = #{deviceType}</if>
        </where>
        order by p.create_time desc
    </select>

    <select id="selectProductShortList" parameterType="com.ssac.iot.domain.Product" resultMap="ProductShortResult">
        select p.product_id,p.product_name from iot_product p
        <where>
            <if test="deptId != null and showSenior and !isAdmin">
                and ( p.tenant_id = #{tenantId}
                    or (p.tenant_id in (
                            SELECT de.dept_user_id
                            FROM sys_dept de
                            WHERE FIND_IN_SET( de.dept_id,(
                            SELECT d.ancestors
                            FROM sys_dept d
                            WHERE d.dept_id = #{deptId} )
                            )
                    )
                    and p.is_sys = 0
                    )
                )
            </if>
            <if test="!showSenior and tenantId != null  and tenantId != 0 and !isAdmin">
                and p.tenant_id = #{tenantId}
            </if>
            <if test="deviceType != null ">
                and device_type = #{deviceType}
            </if>
        </where>
        order by p.create_time desc
    </select>

    <select id="selectProductByProductId" parameterType="Long" resultMap="ProductResult">
        <include refid="selectProductVo"/>
        where product_id = #{productId}
    </select>

    <insert id="insertProduct" parameterType="com.ssac.iot.domain.Product" useGeneratedKeys="true" keyProperty="productId">
        insert into iot_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="tenantName != null and tenantName != ''">tenant_name,</if>
            <if test="isSys != null">is_sys,</if>
            <if test="isAuthorize != null">is_authorize,</if>
            <if test="mqttAccount != null and mqttAccount != ''">mqtt_account,</if>
            <if test="mqttPassword != null and mqttPassword != ''">mqtt_password,</if>
            <if test="mqttSecret != null and mqttSecret != ''">mqtt_secret,</if>
            <if test="status != null">status,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="networkMethod != null">network_method,</if>
            <if test="vertificateMethod != null">vertificate_method,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="protocolCode != null">protocol_code,</if>
            <if test="transport != null" >transport,</if>
            <if test="locationWay != null">location_way,</if>
            <if test="guid != null">guid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="tenantName != null and tenantName != ''">#{tenantName},</if>
            <if test="isSys != null">#{isSys},</if>
            <if test="isAuthorize != null">#{isAuthorize},</if>
            <if test="mqttAccount != null and mqttAccount != ''">#{mqttAccount},</if>
            <if test="mqttPassword != null and mqttPassword != ''">#{mqttPassword},</if>
            <if test="mqttSecret != null and mqttSecret != ''">#{mqttSecret},</if>
            <if test="status != null">#{status},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="networkMethod != null">#{networkMethod},</if>
            <if test="vertificateMethod != null">#{vertificateMethod},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="protocolCode != null">#{protocolCode,jdbcType=VARCHAR}, </if>
            <if test="transport != null" >#{transport,jdbcType=VARCHAR},</if>
            <if test="locationWay != null">#{locationWay},</if>
            <if test="guid != null">#{guid},</if>
         </trim>
    </insert>

    <update id="updateProduct" parameterType="com.ssac.iot.domain.Product">
        update iot_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="tenantName != null and tenantName != ''">tenant_name = #{tenantName},</if>
            <if test="isSys != null">is_sys = #{isSys},</if>
            <if test="isAuthorize != null">is_authorize = #{isAuthorize},</if>
            <if test="mqttAccount != null and mqttAccount != ''">mqtt_account = #{mqttAccount},</if>
            <if test="mqttPassword != null and mqttPassword != ''">mqtt_password = #{mqttPassword},</if>
            <if test="mqttSecret != null and mqttSecret != ''">mqtt_secret = #{mqttSecret},</if>
            <if test="status != null">status = #{status},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="networkMethod != null">network_method = #{networkMethod},</if>
            <if test="vertificateMethod != null">vertificate_method = #{vertificateMethod},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="protocolCode != null">protocol_code = #{protocolCode,jdbcType=VARCHAR}, </if>
            <if test="transport != null" >transport = #{transport,jdbcType=VARCHAR},</if>
            <if test="locationWay != null">location_way = #{locationWay},</if>
            <if test="guid != null">guid = #{guid},</if>
        </trim>
        where product_id = #{productId}
    </update>

    <update id="changeProductStatus" parameterType="com.ssac.iot.model.ChangeProductStatusModel">
        update iot_product set status=#{status}
        where product_id = #{productId}
    </update>

    <update id="updateThingsModelJson" parameterType="com.ssac.iot.domain.Product">
        update iot_product set things_models_json=#{thingsModelsJson}
        where product_id = #{productId}
    </update>

    <delete id="deleteProductByProductId" parameterType="Long">
        delete from iot_product where product_id = #{productId}
    </delete>

    <delete id="deleteProductByProductIds" parameterType="String">
        delete from iot_product where product_id in
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </delete>

    <delete id="deleteProductThingsModelByProductIds" parameterType="String">
        delete from iot_things_model where product_id in
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </delete>

    <select id="firmwareCountInProducts" parameterType="String" resultType="int">
        select count(1) from iot_firmware where product_id in
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>

    <select id="deviceCountInProducts" parameterType="String" resultType="int">
        select count(1) from iot_device where product_id in
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>

    <select id="thingsCountInProduct" parameterType="Long" resultType="int">
        select count(model_id) from iot_things_model
        where product_id = #{productId}
    </select>

    <select id="thingsRepeatCountInProduct" parameterType="Long" resultType="int">
        SELECT count(identifier)
        FROM ( SELECT identifier
               FROM iot_things_model
               WHERE product_id = #{productId}
               GROUP BY identifier,product_id
               HAVING count( identifier )> 1 ) AS identifiers
    </select>

    <select id="getProductBySerialNumber" resultMap="ProductResult" parameterType="String">
        <include refid="selectProductVo" />
        where product_id =
        (select product_id from iot_device
          where serial_number = #{serialNumber,jdbcType=VARCHAR}
        )
    </select>

    <select id="getProtocolBySerialNumber" resultType="com.ssac.iot.model.ProductCode">
        select p.protocol_code as protocolCode,
               p.product_id as productId,
               d.serial_number as serialNumber,
               d.tenant_id as userId,
               p.transport as transport
          from
           iot_product p
           inner join iot_device d on p.product_id = d.product_id
           and d.serial_number = #{serialNumber,jdbcType=VARCHAR}
    </select>

    <select id="getProtocolByProductId" resultType="java.lang.String">
        select p.protocol_code from
        iot_product p
        where p.product_id = #{productId,jdbcType=BIGINT}
    </select>

    <select id="selectByTempleId" resultMap="ProductResult">
        select p.product_id,p.product_name,p.protocol_code,p.transport
        from iot_device_template t
        inner join iot_product p on t.product_id = p.product_id
        where t.template_id = #{templeId}
    </select>

    <select id="selectImgUrlByProductId" resultType="java.lang.String">
        select img_url
        from iot_product
        where product_id = #{productId}
    </select>

    <select id="selectTerminalUserProduct" resultType="com.ssac.iot.domain.Product">
        select distinct p.product_id, p.product_name,p.protocol_code,p.transport, p.category_id,
            p.category_name, p.tenant_id, p.tenant_name, p.is_sys, p.is_authorize,
            p.mqtt_account,p.mqtt_password,p.mqtt_secret ,p.status,p.device_type,
            p.network_method, p.vertificate_method, p.create_time, p.update_time,
            p.img_url,p.remark
        from (
                select device_id
                from iot_device_user
                where user_id = #{tenantId}
                union
                select device_id
                from iot_device_share
                where user_id = #{tenantId}
        ) as u
        inner join iot_device d on u.device_id = d.device_id
        inner join iot_product p on d.product_id = p.product_id
        <where>
            <if test="productName != null  and productName != ''">
                and p.product_name = #{productName}
            </if>
        </where>
    </select>

    <select id="selectProductListByProductIds" resultType="com.ssac.iot.domain.Product">
        <include refid="selectProductVo"/>
        where product_id in
        <foreach collection="productIdList" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>

    <select id="selectGuidByProductId" resultType="java.lang.String">
        select p.guid from iot_product p
        where p.product_id = #{productId}
    </select>

    <select id="selectListScadaIdByGuidS" resultType="com.ssac.iot.domain.Product">
        select id scadaId, guid
        from scada
        where guid in
            <foreach collection="guidList" item="guid" open="(" separator="," close=")">
                #{guid}
            </foreach>
    </select>

</mapper>

