<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.oauth.com/thymeleaf/layout"
      layout:decorator="layout/layout">
<head>
</head>
<body>
<th:block layout:fragment="content">

    <div>
        <label class="control-label">clientId：(客户端的唯一标识符可以用户自定义一般企业缩写)</label>
        <input type="text" class="form-control" name="clientId">

        <label class="control-label">resourceIds：(客户端可以访问的资源集合)</label>
        <input type="text" class="form-control" name="resourceIds">

        <label class="control-label">clientSecret：(客户端密钥,用户或者公司提供也可以系统生成)</label>
        <input type="text" class="form-control" name="clientSecret">

        <label class="control-label">scope：(目前定义有3种 {read write trust})</label>
        <input type="url" class="form-control" name="scope">

        <label class="control-label">authorizedGrantTypes：(授权类型)</label>
        <input type="text" class="form-control" name="authorizedGrantTypes">

        <label class="control-label">webServerRedirectUri：(重定向地址一般是访问的主域名)</label>
        <input type="text" class="form-control" name="webServerRedirectUri">

        <label class="control-label">authorities：(权限集合)</label>
        <input type="email" class="form-control" name="authorities">

        <label class="control-label">accessTokenValidity：(accessToken 有效时间 毫秒值)</label>
        <input type="url" class="form-control" name="accessTokenValidity">

        <label class="control-label">refreshTokenValidity：(refreshToken 有效时间 毫秒值)</label>
        <input type="text" class="form-control" name="refreshTokenValidity">

        <label class="control-label">additional_information：(附加信息)</label>
        <input type="text" class="form-control" name="additional_information">

        <label class="control-label">autoapprove：(是否自动审批通过 true 是 false 否)</label>
        <input type="text" class="form-control" name="autoapprove">

    </div>

</th:block>

</body>