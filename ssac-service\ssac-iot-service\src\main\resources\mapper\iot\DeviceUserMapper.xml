<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.DeviceUserMapper">

    <resultMap type="com.ssac.iot.domain.DeviceUser" id="DeviceUserResult">
        <result property="deviceId"    column="device_id"    />
        <result property="userId"    column="user_id"    />
        <result property="phonenumber"    column="phonenumber"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="userName" column="user_name"/>
    </resultMap>

    <resultMap type="com.ssac.common.core.domain.entity.SysUser" id="ShareUserResult">
        <id     property="userId"       column="user_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="email"        column="email"        />
        <result property="phonenumber"  column="phonenumber"  />
        <result property="sex"          column="sex"          />
        <result property="avatar"       column="avatar"       />
        <result property="createTime"   column="create_time"  />
        <result property="remark"       column="remark"       />
    </resultMap>

    <sql id="selectDeviceUserVo">
        select device_id, user_id, phonenumber, create_time, update_time, remark from iot_device_user
    </sql>

    <select id="selectShareUser" parameterType="com.ssac.iot.domain.DeviceUser" resultMap="ShareUserResult">
        SELECT
            u.user_id,
            u.nick_name,
            u.user_name,
            u.email,
            u.avatar,
            u.phonenumber,
            u.sex,
            u.create_time
        FROM
            sys_user u
                LEFT JOIN (
                SELECT
                    *
                FROM
                    iot_device_user
                WHERE
                    iot_device_user.device_id = #{deviceId}) d on u.user_id = d.user_id

        WHERE
            u.del_flag = '0'
          AND u.STATUS = 0
          AND u.phonenumber = #{phonenumber} and d.device_id is null
    </select>

    <select id="selectDeviceUserList" parameterType="com.ssac.iot.domain.DeviceUser" resultMap="DeviceUserResult">
        <include refid="selectDeviceUserVo"/>
        <where>
            <if test="1==1"> and device_id = #{deviceId}</if>
            <if test="userId != null and userId != 0"> and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectDeviceUserByDeviceId" parameterType="Long" resultMap="DeviceUserResult">
        select d.device_id, d.user_id, d.phonenumber, d.create_time,
               d.update_time, d.remark, su.user_name
        from iot_device_user d left join sys_user su on d.user_id = su.user_id
        where d.device_id = #{deviceId}
    </select>

    <select id="selectDeviceUserByDeviceIdAndUserId" resultMap="DeviceUserResult">
        <include refid="selectDeviceUserVo"/>
        where device_id = #{deviceId} and user_id = #{userId}
    </select>

    <insert id="insertDeviceUser" parameterType="com.ssac.iot.domain.DeviceUser" keyProperty="deviceId">
        insert into iot_device_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="phonenumber != null">phonenumber,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="phonenumber != null">#{phonenumber},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="insertDeviceUserList" parameterType="java.util.ArrayList">
        insert into iot_device_user
        (device_id, user_id,phonenumber, create_time) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.deviceId},#{item.userId},#{item.phonenumber}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updateDeviceUser" parameterType="com.ssac.iot.domain.DeviceUser">
        update iot_device_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="phonenumber != null">phonenumber = #{phonenumber},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where device_id = #{deviceId} and user_id = #{userId}
    </update>

    <delete id="deleteDeviceUserByDeviceId" parameterType="com.ssac.iot.model.UserIdDeviceIdModel">
        delete from iot_device_user
        <where>
            <if test="1==1"> and device_id = #{deviceId}</if>
            <if test="userId != null"> and user_id = #{userId}</if>
        </where>
    </delete>

    <delete id="deleteDeviceUserByDeviceIds" parameterType="String">
        delete from iot_device_user where device_id in
        <foreach item="deviceId" collection="array" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </delete>
    <delete id="deleteDeviceUser">
        delete from iot_device_user where device_id = #{deviceId}  and user_id = #{userId}
    </delete>
</mapper>

