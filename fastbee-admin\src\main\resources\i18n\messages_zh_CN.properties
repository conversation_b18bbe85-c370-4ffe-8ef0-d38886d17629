#\u9519\u8BEF\u6D88\u606F
not.null=* \u5FC5\u987B\u586B\u5199
user.jcaptcha.error=\u9A8C\u8BC1\u7801\u9519\u8BEF
user.jcaptcha.expire=\u9A8C\u8BC1\u7801\u5DF2\u5931\u6548
user.not.exists=\u7528\u6237\u4E0D\u5B58\u5728/\u5BC6\u7801\u9519\u8BEF
user.password.not.match=\u7528\u6237\u4E0D\u5B58\u5728/\u5BC6\u7801\u9519\u8BEF
user.password.retry.limit.count=\u5BC6\u7801\u8F93\u5165\u9519\u8BEF{0}\u6B21
user.password.retry.limit.exceed=\u5BC6\u7801\u8F93\u5165\u9519\u8BEF{0}\u6B21\uFF0C\u5E10\u6237\u9501\u5B9A{1}\u5206\u949F
user.password.delete=\u5BF9\u4E0D\u8D77\uFF0C\u60A8\u7684\u8D26\u53F7\u5DF2\u88AB\u5220\u9664
user.blocked=\u7528\u6237\u5DF2\u5C01\u7981\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
role.blocked=\u89D2\u8272\u5DF2\u5C01\u7981\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
user.logout.success=\u9000\u51FA\u6210\u529F

length.not.valid=\u957F\u5EA6\u5FC5\u987B\u5728{min}\u5230{max}\u4E2A\u5B57\u7B26\u4E4B\u95F4

user.username.not.valid=* 2\u523020\u4E2A\u6C49\u5B57\u3001\u5B57\u6BCD\u3001\u6570\u5B57\u6216\u4E0B\u5212\u7EBF\u7EC4\u6210\uFF0C\u4E14\u5FC5\u987B\u4EE5\u975E\u6570\u5B57\u5F00\u5934
user.password.not.valid=* 5-50\u4E2A\u5B57\u7B26

user.email.not.valid=\u90AE\u7BB1\u683C\u5F0F\u9519\u8BEF
user.mobile.phone.number.not.valid=\u624B\u673A\u53F7\u683C\u5F0F\u9519\u8BEF
user.login.success=\u767B\u5F55\u6210\u529F
user.register.success=\u6CE8\u518C\u6210\u529F
user.notfound=\u8BF7\u91CD\u65B0\u767B\u5F55
user.forcelogout=\u7BA1\u7406\u5458\u5F3A\u5236\u9000\u51FA\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55
user.unknown.error=\u672A\u77E5\u9519\u8BEF\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55

##\u6743\u9650
no.permission=\u60A8\u6CA1\u6709\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.create.permission=\u60A8\u6CA1\u6709\u521B\u5EFA\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.update.permission=\u60A8\u6CA1\u6709\u4FEE\u6539\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.delete.permission=\u60A8\u6CA1\u6709\u5220\u9664\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.export.permission=\u60A8\u6CA1\u6709\u5BFC\u51FA\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]
no.view.permission=\u60A8\u6CA1\u6709\u67E5\u770B\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u6DFB\u52A0\u6743\u9650 [{0}]

##\u6587\u4EF6\u4E0A\u4F20\u6D88\u606F
upload.exceed.maxSize=\u4E0A\u4F20\u7684\u6587\u4EF6\u5927\u5C0F\u8D85\u51FA\u9650\u5236\u7684\u6587\u4EF6\u5927\u5C0F\uFF01<br/>\u5141\u8BB8\u7684\u6587\u4EF6\u6700\u5927\u5927\u5C0F\u662F\uFF1A{0}MB\uFF01
upload.filename.exceed.length=\u4E0A\u4F20\u7684\u6587\u4EF6\u540D\u6700\u957F{0}\u4E2A\u5B57\u7B26
upload.success=\u4E0A\u4F20\u6210\u529F

##\u6587\u4EF6\u4E0B\u8F7D\u6D88\u606F
download.filename.not.valid=\u6587\u4EF6\u540D\u79F0[{}]\u975E\u6CD5\uFF0C\u4E0D\u5141\u8BB8\u4E0B\u8F7D
download.file.failed=\u4E0B\u8F7D\u6587\u4EF6\u5931\u8D25
download.resource.not.valid=\u8D44\u6E90\u6587\u4EF6[{}]\u975E\u6CD5\uFF0C\u4E0D\u5141\u8BB8\u4E0B\u8F7D

##Dept
dept.add.failed.name.exists=\u65B0\u589E\u673A\u6784[{}]\u5931\u8D25\uFF0C\u673A\u6784\u540D\u79F0\u5DF2\u5B58\u5728
dept.update.failed.name.exists=\u4FEE\u6539\u673A\u6784[{}]\u5931\u8D25\uFF0C\u673A\u6784\u540D\u79F0\u5DF2\u5B58\u5728
dept.update.failed.parent.not.valid=\u4FEE\u6539\u673A\u6784[{}]\u5931\u8D25\uFF0C\u4E0A\u7EA7\u673A\u6784\u4E0D\u80FD\u662F\u81EA\u5DF1
dept.update.failed.child.not.valid=\u8BE5\u673A\u6784\u5305\u542B\u672A\u505C\u7528\u7684\u5B50\u673A\u6784\uFF01
dept.delete.failed.child.exists=\u5B58\u5728\u4E0B\u7EA7\u673A\u6784\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664
dept.delete.failed.user.exists=\u673A\u6784\u5B58\u5728\u7528\u6237\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664

##Dict
dict.add.failed.type.exists=\u65B0\u589E\u5B57\u5178[{}]\u5931\u8D25\uFF0C\u5B57\u5178\u7C7B\u578B\u5DF2\u5B58\u5728
dict.update.failed.type.exists=\u65B0\u589E\u5B57\u5178[{}]\u5931\u8D25\uFF0C\u5B57\u5178\u7C7B\u578B\u5DF2\u5B58\u5728

##Index
index.welcome.message=\u6B22\u8FCE\u4F7F\u7528{}\u540E\u53F0\u7BA1\u7406\u6846\u67B6\uFF0C\u5F53\u524D\u7248\u672C\uFF1Av{}\uFF0C\u8BF7\u901A\u8FC7\u524D\u7AEF\u5730\u5740\u8BBF\u95EE\u3002

##Menu
menu.add.failed.name.exists=\u65B0\u589E\u83DC\u5355[{}]\u5931\u8D25\uFF0C\u83DC\u5355\u540D\u79F0\u5DF2\u5B58\u5728
menu.add.failed.path.not.valid=\u65B0\u589E\u83DC\u5355[{}]\u5931\u8D25\uFF0C\u5730\u5740\u5FC5\u987B\u4EE5http(s)://\u5F00\u5934
menu.update.failed.name.exists=\u4FEE\u6539\u83DC\u5355[{}]\u5931\u8D25\uFF0C\u83DC\u5355\u540D\u79F0\u5DF2\u5B58\u5728
menu.update.failed.path.not.valid=\u4FEE\u6539\u83DC\u5355[{}]\u5931\u8D25\uFF0C\u5730\u5740\u5FC5\u987B\u4EE5http(s)://\u5F00\u5934
menu.update.failed.parent.not.valid=\u4FEE\u6539\u83DC\u5355[{}]\u5931\u8D25\uFF0C\u4E0A\u7EA7\u83DC\u5355\u4E0D\u80FD\u9009\u62E9\u81EA\u5DF1
menu.delete.failed.child.exists=\u5B58\u5728\u5B50\u83DC\u5355,\u4E0D\u5141\u8BB8\u5220\u9664
menu.delete.failed.role.exists=\u83DC\u5355\u5DF2\u5206\u914D,\u4E0D\u5141\u8BB8\u5220\u9664

##Post
post.add.failed.name.exists=\u65B0\u589E\u5C97\u4F4D[{}]\u5931\u8D25\uFF0C\u5C97\u4F4D\u540D\u79F0\u5DF2\u5B58\u5728
post.add.failed.code.exists=\u65B0\u589E\u5C97\u4F4D[{}]\u5931\u8D25\uFF0C\u5C97\u4F4D\u7F16\u7801\u5DF2\u5B58\u5728
post.update.failed.name.exists=\u4FEE\u6539\u5C97\u4F4D[{}]\u5931\u8D25\uFF0C\u5C97\u4F4D\u540D\u79F0\u5DF2\u5B58\u5728
post.update.failed.code.exists=\u4FEE\u6539\u5C97\u4F4D[{}]\u5931\u8D25\uFF0C\u5C97\u4F4D\u7F16\u7801\u5DF2\u5B58\u5728

##User
user.username.exists=\u7CFB\u7EDF\u8D26\u53F7\u540D\u79F0\u5DF2\u5B58\u5728\uFF0C\u8BF7\u4FEE\u6539\u540E\u91CD\u8BD5
user.password.differ=\u4E24\u6B21\u5BC6\u7801\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
user.add.failed.name.exists=\u65B0\u589E\u7528\u6237[{}]\u5931\u8D25\uFF0C\u767B\u5F55\u8D26\u53F7\u5DF2\u5B58\u5728
user.add.failed.phone.exists=\u65B0\u589E\u7528\u6237[{}]\u5931\u8D25\uFF0C\u624B\u673A\u53F7\u7801\u5DF2\u5B58\u5728
user.add.failed.email.exists=\u65B0\u589E\u7528\u6237[{}]\u5931\u8D25\uFF0C\u90AE\u7BB1\u8D26\u53F7\u5DF2\u5B58\u5728
user.update.failed.password.wrong=\u4FEE\u6539\u5BC6\u7801\u5931\u8D25\uFF0C\u65E7\u5BC6\u7801\u9519\u8BEF
user.update.failed.password.repeat=\u65B0\u5BC6\u7801\u4E0D\u80FD\u4E0E\u65E7\u5BC6\u7801\u76F8\u540C
user.update.password.failed=\u4FEE\u6539\u5BC6\u7801\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
user.update.failed.name.exists=\u65B0\u589E\u7528\u6237[{}]\u5931\u8D25\uFF0C\u767B\u5F55\u8D26\u53F7\u5DF2\u5B58\u5728
user.update.failed.phone.exists=\u4FEE\u6539\u7528\u6237[{}]\u5931\u8D25\uFF0C\u624B\u673A\u53F7\u7801\u5DF2\u5B58\u5728
user.update.failed.email.exists=\u4FEE\u6539\u7528\u6237[{}]\u5931\u8D25\uFF0C\u90AE\u7BB1\u8D26\u53F7\u5DF2\u5B58\u5728
user.update.failed=\u4FEE\u6539\u4E2A\u4EBA\u4FE1\u606F\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
user.delete.failed=\u5F53\u524D\u7528\u6237\u4E0D\u80FD\u5220\u9664
user.upload.avatar.failed=\u4E0A\u4F20\u56FE\u7247\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
user.not.login=\u8BF7\u767B\u5F55\u540E\u91CD\u8BD5
user.access.denied=\u7528\u6237\u62D2\u7EDD\u8BBF\u95EE

##Role
role.add.manager.failed=\u4E0D\u5141\u8BB8\u8BBE\u7F6E\u7BA1\u7406\u5458\u89D2\u8272\u6807\u8BC6
role.add.failed.name.exists=\u65B0\u589E\u89D2\u8272[{}]\u5931\u8D25\uFF0C\u89D2\u8272\u540D\u79F0\u5DF2\u5B58\u5728
role.add.failed.key.exists=\u65B0\u589E\u89D2\u8272[{}]\u5931\u8D25\uFF0C\u89D2\u8272\u6743\u9650\u5DF2\u5B58\u5728
role.update.failed.name.exists=\u4FEE\u6539\u89D2\u8272[{}]\u5931\u8D25\uFF0C\u89D2\u8272\u540D\u79F0\u5DF2\u5B58\u5728
role.update.failed.key.exists=\u4FEE\u6539\u89D2\u8272[{}]\u5931\u8D25\uFF0C\u89D2\u8272\u6743\u9650\u5DF2\u5B58\u5728
role.update.failed=\u4FEE\u6539\u89D2\u8272[{}]\u5931\u8D25\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458

##Import
import.failed.file.null=\u5BFC\u5165\u5931\u8D25\uFF0C\u8BF7\u5148\u4E0A\u4F20\u6587\u4EF6\uFF01
import.failed.data.null=\u5BFC\u5165\u5931\u8D25\uFF0C\u5BFC\u5165\u6570\u636E\u4E3A\u7A7A\uFF01
import.failed.device.name.null=\u5BFC\u5165\u5931\u8D25\uFF0C\u6A21\u677F\u91CC\u8BBE\u5907\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A\uFF01
import.success=\u5BFC\u5165\u6210\u529F

##General
success=\u6210\u529F
fail=\u5931\u8D25
query.success=\u67E5\u8BE2\u6210\u529F
operate.success=\u64CD\u4F5C\u6210\u529F
create.success=\u521B\u5EFA\u6210\u529F
create.failed=\u521B\u5EFA\u5931\u8D25
save.success=\u4FDD\u5B58\u6210\u529F
save.failed=\u4FDD\u5B58\u5931\u8D25
authorization.success=\u6388\u6743\u6210\u529F

##Email
email.format.error=\u90AE\u7BB1\u683C\u5F0F\u9519\u8BEF
email.verification.code.send=\u90AE\u7BB1\u9A8C\u8BC1\u7801\u5DF2\u53D1\u9001

##Firmware
firmware.task.upgrade.failed.time.not.valid=\u9884\u5B9A\u5347\u7EA7\u65F6\u95F4\u5E94\u5927\u4E8E\u5F53\u524D\u65F6\u95F4
##Media
media.record.query.failed=\u8FDE\u63A5\u8D85\u65F6\u6216\u53D1\u751F\u9519\u8BEF\uFF0C\u672A\u83B7\u53D6\u5230\u6570\u636E
##Modbus
modbus.type.null=\u7C7B\u578B\u4E3A\u7A7A
##Netty
netty.client.not.exists=\u5BA2\u6237\u7AEF\u4E0D\u5B58\u5728
##Runtime
runtime.message.id.null=\u6D88\u606Fid\u4E3A\u7A7A
##Wechat
wechat.verify.type.null=\u8BF7\u4F20\u5165\u9A8C\u8BC1\u65B9\u5F0F
wechat.bind.message.id.null=\u8BF7\u4F20\u5165\u7ED1\u5B9A\u4FE1\u606FID
##AuthResource
auth.resource.product.query.success=\u67E5\u8BE2\u4EA7\u54C1\u5217\u8868\u6210\u529F
##Device
device.user.id.null=\u7528\u6237ID\u4E0D\u80FD\u4E3A\u7A7A
device.product.id.null=\u8BBE\u5907\u7F16\u53F7\u548C\u4EA7\u54C1ID\u4E0D\u80FD\u4E3A\u7A7A
device.dept.id.null=\u8BF7\u9009\u62E9\u5206\u914D\u673A\u6784
device.id.null=\u8BF7\u9009\u62E9\u8BBE\u5907
##DeviceJob
job.add.failed.cron.not.valid=\u65B0\u589E\u4EFB\u52A1[{}]\u5931\u8D25\uFF0CCron\u8868\u8FBE\u5F0F\u4E0D\u6B63\u786E
job.add.failed.rmi.not.valid=\u65B0\u589E\u4EFB\u52A1[{}]\u5931\u8D25\uFF0C\u76EE\u6807\u5B57\u7B26\u4E32\u4E0D\u5141\u8BB8'rmi'\u8C03\u7528
job.add.failed.ldap.not.valid=\u65B0\u589E\u4EFB\u52A1[{}]\u5931\u8D25\uFF0C\u76EE\u6807\u5B57\u7B26\u4E32\u4E0D\u5141\u8BB8'ldap(s)'\u8C03\u7528
job.add.failed.http.not.valid=\u65B0\u589E\u4EFB\u52A1[{}]\u5931\u8D25\uFF0C\u76EE\u6807\u5B57\u7B26\u4E32\u4E0D\u5141\u8BB8'http(s)'\u8C03\u7528
job.add.failed.string.error=\u65B0\u589E\u4EFB\u52A1[{}]\u5931\u8D25\uFF0C\u76EE\u6807\u5B57\u7B26\u4E32\u5B58\u5728\u8FDD\u89C4
job.add.failed.string.not.valid=\u65B0\u589E\u4EFB\u52A1[{}]\u5931\u8D25\uFF0C\u76EE\u6807\u5B57\u7B26\u4E32\u4E0D\u5728\u767D\u540D\u5355\u5185
job.update.failed.cron.not.valid=\u4FEE\u6539\u4EFB\u52A1[{}]\u5931\u8D25\uFF0CCron\u8868\u8FBE\u5F0F\u4E0D\u6B63\u786E
job.update.failed.rmi.not.valid=\u4FEE\u6539\u4EFB\u52A1[{}]\u5931\u8D25\uFF0C\u76EE\u6807\u5B57\u7B26\u4E32\u4E0D\u5141\u8BB8'rmi'\u8C03\u7528
job.update.failed.ldap.not.valid=\u4FEE\u6539\u4EFB\u52A1[{}]\u5931\u8D25\uFF0C\u76EE\u6807\u5B57\u7B26\u4E32\u4E0D\u5141\u8BB8'ldap(s)'\u8C03\u7528
job.update.failed.http.not.valid=\u4FEE\u6539\u4EFB\u52A1[{}]\u5931\u8D25\uFF0C\u76EE\u6807\u5B57\u7B26\u4E32\u4E0D\u5141\u8BB8'http(s)'\u8C03\u7528
job.update.failed.string.error=\u4FEE\u6539\u4EFB\u52A1[{}]\u5931\u8D25\uFF0C\u76EE\u6807\u5B57\u7B26\u4E32\u5B58\u5728\u8FDD\u89C4
job.update.failed.string.not.valid=\u4FEE\u6539\u4EFB\u52A1[{}]\u5931\u8D25\uFF0C\u76EE\u6807\u5B57\u7B26\u4E32\u4E0D\u5728\u767D\u540D\u5355\u5185
job.not.exists=\u4EFB\u52A1\u4E0D\u5B58\u5728\u6216\u5DF2\u8FC7\u671F
##DeviceUser
device.user.delete.failed.user.not.valid=\u8BBE\u5907\u6240\u6709\u8005\u4E0D\u80FD\u5220\u9664
##GoviewProject
goview.project.data.save.failed.id.null=\u6CA1\u6709\u8BE5\u9879\u76EEID
goview.project.data.execute.sql.failed=\u8BF7\u7F16\u5199sql\u8BED\u53E5
##ThingsModel
things.model.identifier.repeat=\u4EA7\u54C1\u4E0B\u7684\u6807\u8BC6\u7B26\u4E0D\u80FD\u91CD\u590D
things.model.import.failed.identifier.repeat=[{}]\u6761\u6570\u636E\u672A\u5BFC\u5165\uFF0C\u6807\u8BC6\u7B26\u91CD\u590D
##MQTT
mqtt.unauthorized=mqtt\u8D26\u53F7\u548C\u5BC6\u7801\u4E0E\u8BA4\u8BC1\u670D\u52A1\u5668\u914D\u7F6E\u4E0D\u5339\u914D
##Oauth
oauth.response.type.not.valid=response_type\u53C2\u6570\u503C\u53EA\u5141\u8BB8code\u548Ctoken
oauth.grant.type.null=\u672A\u77E5\u6388\u6743\u7C7B\u578B
oauth.grant.type.implicit.not.support=Token\u63A5\u53E3\u4E0D\u652F\u6301implicit\u6388\u6743\u6A21\u5F0F
oauth.access.token.null=\u8BBF\u95EE\u4EE4\u724C\u4E0D\u80FD\u4E3A\u7A7A
obtain.basic.authorization.failed=client_id\u6216client_secret\u672A\u6B63\u786E\u4F20\u9012
##Record
record.app.null=app\u4E0D\u80FD\u4E3A\u7A7A
record.stream.null=stream\u4E0D\u80FD\u4E3A\u7A7A
record.time.not.valid=\u9519\u8BEF\u7684\u5F00\u59CB\u65F6\u95F4\u6216\u7ED3\u675F\u65F6\u95F4
record.file.null=\u672A\u627E\u5230\u89C6\u9891\u6587\u4EF6

##ErrorCodeConstants
app.not.found=App \u4E0D\u5B58\u5728
app.is.disable=App \u5DF2\u7ECF\u88AB\u7981\u7528
app.exist.order.cant.delete=\u652F\u4ED8\u5E94\u7528\u5B58\u5728\u652F\u4ED8\u8BA2\u5355\uFF0C\u65E0\u6CD5\u5220\u9664
app.exist.refund.cant.delete=\u652F\u4ED8\u5E94\u7528\u5B58\u5728\u9000\u6B3E\u8BA2\u5355\uFF0C\u65E0\u6CD5\u5220\u9664
channel.not.found=\u652F\u4ED8\u6E20\u9053\u7684\u914D\u7F6E\u4E0D\u5B58\u5728
channel.is.disable=\u652F\u4ED8\u6E20\u9053\u5DF2\u7ECF\u7981\u7528
channel.exists.same.channel.error=\u5DF2\u5B58\u5728\u76F8\u540C\u7684\u6E20\u9053
order.not.found=\u652F\u4ED8\u8BA2\u5355\u4E0D\u5B58\u5728
order.status.is.not.waiting=\u652F\u4ED8\u8BA2\u5355\u4E0D\u5904\u4E8E\u5F85\u652F\u4ED8
order.status.is.success=\u8BA2\u5355\u5DF2\u652F\u4ED8\uFF0C\u8BF7\u5237\u65B0\u9875\u9762
order.is.expired=\u652F\u4ED8\u8BA2\u5355\u5DF2\u7ECF\u8FC7\u671F
order.submit.channel.error=\u53D1\u8D77\u652F\u4ED8\u62A5\u9519\uFF0C\u9519\u8BEF\u7801\uFF1A{}\uFF0C\u9519\u8BEF\u63D0\u793A\uFF1A{}
order.refund.fail.status.error=\u652F\u4ED8\u8BA2\u5355\u9000\u6B3E\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A\u72B6\u6001\u4E0D\u662F\u5DF2\u652F\u4ED8\u6216\u5DF2\u9000\u6B3E
order.extension.not.found=\u652F\u4ED8\u4EA4\u6613\u62D3\u5C55\u5355\u4E0D\u5B58\u5728
order.extension.status.is.not.waiting=\u652F\u4ED8\u4EA4\u6613\u62D3\u5C55\u5355\u4E0D\u5904\u4E8E\u5F85\u652F\u4ED8
order.extension.is.paid=\u8BA2\u5355\u5DF2\u652F\u4ED8\uFF0C\u8BF7\u7B49\u5F85\u652F\u4ED8\u7ED3\u679C
refund.price.exceed=\u9000\u6B3E\u91D1\u989D\u8D85\u8FC7\u8BA2\u5355\u53EF\u9000\u6B3E\u91D1\u989D
refund.has.refunding=\u5DF2\u7ECF\u6709\u9000\u6B3E\u5728\u5904\u7406\u4E2D
refund.exists=\u5DF2\u7ECF\u5B58\u5728\u9000\u6B3E\u5355
refund.not.found=\u652F\u4ED8\u9000\u6B3E\u5355\u4E0D\u5B58\u5728
refund.statue.is.not.waiting=\u652F\u4ED8\u9000\u6B3E\u5355\u4E0D\u5904\u4E8E\u5F85\u9000\u6B3E
demo.order.not.found=\u793A\u4F8B\u8BA2\u5355\u4E0D\u5B58\u5728
demo.order.update.paid.status.not.unpaid=\u793A\u4F8B\u8BA2\u5355\u66F4\u65B0\u652F\u4ED8\u72B6\u6001\u5931\u8D25\uFF0C\u8BA2\u5355\u4E0D\u662F\u3010\u672A\u652F\u4ED8\u3011\u72B6\u6001
demo.order.update.paid.fail.pay.order.id.error=\u793A\u4F8B\u8BA2\u5355\u66F4\u65B0\u652F\u4ED8\u72B6\u6001\u5931\u8D25\uFF0C\u652F\u4ED8\u5355\u7F16\u53F7\u4E0D\u5339\u914D
demo.order.update.paid.fail.pay.order.status.not.success=\u793A\u4F8B\u8BA2\u5355\u66F4\u65B0\u652F\u4ED8\u72B6\u6001\u5931\u8D25\uFF0C\u652F\u4ED8\u5355\u72B6\u6001\u4E0D\u662F\u3010\u652F\u4ED8\u6210\u529F\u3011\u72B6\u6001
demo.order.update.paid.fail.pay.price.not.match=\u793A\u4F8B\u8BA2\u5355\u66F4\u65B0\u652F\u4ED8\u72B6\u6001\u5931\u8D25\uFF0C\u652F\u4ED8\u5355\u91D1\u989D\u4E0D\u5339\u914D
demo.order.refund.fail.not.paid=\u53D1\u8D77\u9000\u6B3E\u5931\u8D25\uFF0C\u793A\u4F8B\u8BA2\u5355\u672A\u652F\u4ED8
demo.order.refund.fail.refunded=\u53D1\u8D77\u9000\u6B3E\u5931\u8D25\uFF0C\u793A\u4F8B\u8BA2\u5355\u5DF2\u9000\u6B3E
demo.order.refund.fail.refund.not.found=\u53D1\u8D77\u9000\u6B3E\u5931\u8D25\uFF0C\u9000\u6B3E\u8BA2\u5355\u4E0D\u5B58\u5728
demo.order.refund.fail.refund.not.success=\u53D1\u8D77\u9000\u6B3E\u5931\u8D25\uFF0C\u9000\u6B3E\u8BA2\u5355\u672A\u9000\u6B3E\u6210\u529F
demo.order.refund.fail.refund.order.id.error=\u53D1\u8D77\u9000\u6B3E\u5931\u8D25\uFF0C\u9000\u6B3E\u5355\u7F16\u53F7\u4E0D\u5339\u914D
demo.order.refund.fail.refund.price.not.match=\u53D1\u8D77\u9000\u6B3E\u5931\u8D25\uFF0C\u9000\u6B3E\u5355\u91D1\u989D\u4E0D\u5339\u914D
device.can.send=\u6682\u65E0\u6743\u9650\u64CD\u4F5C
