package com.fastbee.nfzx;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fastbee.common.annotation.SysProtocol;
import com.fastbee.common.constant.FastBeeConstant;
import com.fastbee.common.core.mq.DeviceReport;
import com.fastbee.common.core.mq.DeviceReportBo;
import com.fastbee.common.core.mq.MQSendMessageBo;
import com.fastbee.common.core.mq.message.DeviceData;
import com.fastbee.common.core.mq.message.FunctionCallBackBo;
import com.fastbee.common.core.thingsModel.ThingsModelSimpleItem;
import com.fastbee.common.enums.ServerType;
import com.fastbee.common.utils.DateUtils;
import com.fastbee.common.utils.gateway.mq.TopicsUtils;
import com.ssac.mqttclient.IEmqxMessageProducer;
import com.fastbee.protocol.base.protocol.IProtocol;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;

import static cn.hutool.core.date.DatePattern.PURE_DATETIME_FORMAT;

/**
 * 南方之星JSON协议服务实现类
 * 实现了IProtocol接口，用于处理南方之星协议的设备数据编解码
 * 
 * <AUTHOR> Team
 * <AUTHOR> Team (移植适配)
 */
@Slf4j
@Component
@SysProtocol(name = "南方之星协议", protocolCode = FastBeeConstant.PROTOCOL.NFZX_JSON, description = "南方之星协议")
public class NFZXJsonParkProtocolService implements IProtocol {

    // MQTT消息生产者，用于发送告警消息
    @Resource
    private IEmqxMessageProducer emqxMessageSerice;

    // 主题工具类，用于解析MQTT主题信息
    @Resource
    private TopicsUtils topicsUtils;

    /**
     * 8001/8002命令中数组参数的长度映射表
     * key: 参数名
     * value: 对应的数组长度
     */
    private static final Map<String, Integer> ID_8001_MAP = new HashMap<String, Integer>() {{
        put("VP", 3);   // 电压参数数组长度
        put("AP", 3);    // 电流参数数组长度
        put("VOLT", 3);
        put("AMP", 3);
        put("Temp", 4);
        put("PT", 3);
        put("QT", 3);
        put("ST", 3);
        put("PFT", 3);
        put("THDI", 3);
        put("THDV", 3);
        put("DEPT", 4);
        put("MEPT", 4);
        put("TEPT", 4);
        put("DEQT", 4);
        put("MEQT", 4);
        put("TEQT", 4);
    }};

    /**
     * 参数值转换系数映射表
     * key: 参数名
     * value: 原始值需要乘以的系数
     */
    private static final Map<String, Double> KEY_MULTIPLIER_MAP = new HashMap<String, Double>() {{
        put("VP", 0.01);  // 电压参数转换系数
        put("AP", 0.01);  // 电流参数转换系数
        put("VOLT", 0.01);
        put("AMP", 0.001);
        put("Temp", 0.1);
        put("PT", 0.0001);
        put("QT", 0.0001);
        put("ST", 0.0001);
        put("PFT", 0.001);
        put("DEPT", 0.001);
        put("MEPT", 0.001);
        put("TEPT", 0.001);
        put("DEQT", 0.001);
        put("MEQT", 0.001);
        put("TEQT", 0.001);
    }};

    /**
     * 参数值计算函数映射表
     * 在init方法中初始化，根据KEY_MULTIPLIER_MAP自动生成
     */
    private static final Map<String, Function<Object, String>> ID_FUNC_MAP = new HashMap<>();

    /**
     * 需要回调的设备命令集合
     */
    private static final Set<Integer> CALL_BACK_DEVICE_CMD = new HashSet<Integer>() {{
        add(8002);  // 告警上报命令
        add(8003);
        add(8004);
        add(8005);
        add(8006);
        add(8007);
    }};

    /**
     * 告警参数名称列表
     * 与AMap字段中的告警位一一对应
     */
    private static final List<String> A_MAP_W = Lists.newArrayList(
            "byqfhw",  // 备用切换位
            "aww",     // A相电压告警
            "bww",
            "cww",
            "avw",
            "bww",
            "cww",
            "avqw",
            "bvqw",
            "cvqw",
            "axqw",
            "bxqw",
            "cxqw",
            "axglw",
            "bxglw",
            "cxglw",
            "ldw",
            "wd1w",
            "wd2w",
            "wd3w",
            "wd4w",
            "aixw",
            "bixw",
            "cixw",
            "avxw",
            "bvxw",
            "cvxw"
    );

    /**
     * 初始化方法，在Bean创建后执行
     * 为每个参数创建对应的计算函数
     */
    @PostConstruct
    private void init() {
        KEY_MULTIPLIER_MAP.forEach((key, multiplier) ->
                ID_FUNC_MAP.put(key, data -> {
                    if (data == null) {
                        return "0.00";
                    }
                    // 对整型数据应用转换系数并格式化为两位小数
                    if (data instanceof Integer) {
                        return String.format("%.2f", (Integer)data * multiplier);
                    }
                    return String.valueOf(data);
                })
        );
        log.info("NFZX协议初始化完成，加载{}个参数转换函数", ID_FUNC_MAP.size());
    }

    /**
     * 解码设备上报数据
     * @param deviceData 设备原始数据
     * @param clientId 客户端ID
     * @return 解码后的设备报告
     */
    @Override
    public DeviceReport decode(DeviceData deviceData, String clientId) {
        try {
            DeviceReport reportMessage = new DeviceReport();
            // 解析JSON数据
            String data = new String(deviceData.getData(), StandardCharsets.UTF_8);
            log.debug("NFZX协议解码数据: clientId={}, data={}", clientId, data);
            
            JSONObject params = JSONObject.parseObject(data);
            List<ThingsModelSimpleItem> result = new ArrayList<>();
            
            // 获取命令类型
            Integer cmd = params.getInteger("cmd");
            if (cmd == null) {
                log.warn("NFZX协议数据中缺少cmd字段: clientId={}", clientId);
                cmd = 0;
            }

            // 处理8001/8002命令的特殊逻辑
            if (Integer.valueOf(8001).equals(cmd) || Integer.valueOf(8002).equals(cmd)) {
                result.addAll(processSpecialCommand(params, cmd, clientId));
            } else {
                // 处理其他命令的普通参数
                result.addAll(processNormalCommand(params, clientId));
            }

            // 设置报告消息属性
            reportMessage.setThingsModelSimpleItem(result);
            reportMessage.setIsPackage(true);
            reportMessage.setMessageId("0");
            reportMessage.setClientId(clientId);
            reportMessage.setSerialNumber(clientId);
            reportMessage.setProtocolCode(FastBeeConstant.PROTOCOL.NFZX_JSON);
            reportMessage.setSources(data);
            reportMessage.setCallbackDevice(CALL_BACK_DEVICE_CMD.contains(cmd));
            reportMessage.setCmd(cmd);
            
            log.debug("NFZX协议解码成功: clientId={}, cmd={}, 解析出{}个属性", clientId, cmd, result.size());
            return reportMessage;
        } catch (Exception e) {
            log.error("NFZX协议数据解析异常: clientId={}, error={}", clientId, e.getMessage(), e);
            throw new RuntimeException("NFZX协议数据解析异常: " + e.getMessage());
        }
    }

    /**
     * 编码平台下发的指令
     * @param message 平台消息
     * @return 编码后的回调对象
     */
    @Override
    public FunctionCallBackBo encode(MQSendMessageBo message) {
        try {
            FunctionCallBackBo callBack = new FunctionCallBackBo();
            JSONObject deviceMsg = new JSONObject();

            // 特殊处理2001命令
            if (Integer.valueOf(2001).equals(Integer.valueOf(message.getValue()))) {
                deviceMsg = assembly2001(message.getSerialNumber());
            } else {
                // 构建普通命令的JSON结构
                deviceMsg.put("cmd", message.getValue());
                deviceMsg.put("sn", message.getSerialNumber());
                deviceMsg.put("time", DateUtil.format(new Date(), PURE_DATETIME_FORMAT));
                deviceMsg.putAll(message.getParams());
            }

            String msg = deviceMsg.toString();
            // 设置回调消息属性
            callBack.setMessage(msg.getBytes(StandardCharsets.UTF_8));
            callBack.setSources(msg);
            callBack.setTopicName("/sub/" + message.getSerialNumber());

            log.debug("NFZX协议编码成功: device={}, cmd={}, topic={}", 
                    message.getSerialNumber(), message.getValue(), callBack.getTopicName());
            return callBack;
        } catch (Exception e) {
            log.error("NFZX协议指令编码异常: device={}, cmd={}, error={}", 
                    message.getSerialNumber(), message.getValue(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理特殊命令（8001/8002）的数据解析
     * @param params JSON参数
     * @param cmd 命令类型
     * @param clientId 客户端ID
     * @return 解析后的物模型数据列表
     */
    private List<ThingsModelSimpleItem> processSpecialCommand(JSONObject params, Integer cmd, String clientId) {
        List<ThingsModelSimpleItem> result = new ArrayList<>();

        // 处理数组参数
        for (Map.Entry<String, Integer> entry : ID_8001_MAP.entrySet()) {
            String key = entry.getKey();
            Integer arrayLength = entry.getValue();

            if (params.containsKey(key)) {
                JSONArray array = params.getJSONArray(key);
                if (array != null && array.size() >= arrayLength) {
                    for (int i = 0; i < arrayLength; i++) {
                        ThingsModelSimpleItem item = new ThingsModelSimpleItem();
                        item.setTs(DateUtils.getNowDate());
                        item.setId(key + (i + 1));

                        Object value = array.get(i);
                        // 应用转换函数
                        Function<Object, String> func = ID_FUNC_MAP.get(key);
                        if (func != null) {
                            item.setValue(func.apply(value));
                        } else {
                            item.setValue(String.valueOf(value));
                        }
                        result.add(item);
                    }
                }
            }
        }

        // 处理普通参数
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 跳过已处理的数组参数和系统字段
            if (ID_8001_MAP.containsKey(key) || "cmd".equals(key) || "sn".equals(key) || "time".equals(key)) {
                continue;
            }

            ThingsModelSimpleItem item = new ThingsModelSimpleItem();
            item.setTs(DateUtils.getNowDate());
            item.setId(key);
            item.setValue(String.valueOf(value));
            result.add(item);
        }

        // 处理8002命令的告警数据
        if (Integer.valueOf(8002).equals(cmd)) {
            processAlarmData(params, clientId);
        }

        return result;
    }

    /**
     * 处理普通命令的数据解析
     * @param params JSON参数
     * @param clientId 客户端ID
     * @return 解析后的物模型数据列表
     */
    private List<ThingsModelSimpleItem> processNormalCommand(JSONObject params, String clientId) {
        List<ThingsModelSimpleItem> result = new ArrayList<>();

        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 跳过系统字段
            if ("cmd".equals(key) || "sn".equals(key) || "time".equals(key)) {
                continue;
            }

            ThingsModelSimpleItem item = new ThingsModelSimpleItem();
            item.setTs(DateUtils.getNowDate());
            item.setId(key);
            item.setValue(String.valueOf(value));
            result.add(item);
        }

        return result;
    }

    /**
     * 处理告警数据并发送MQTT告警消息
     * @param params JSON参数
     * @param clientId 客户端ID
     */
    private void processAlarmData(JSONObject params, String clientId) {
        try {
            Object aMapObj = params.get("AMap");
            if (aMapObj instanceof Integer) {
                Integer aMap = (Integer) aMapObj;

                // 解析告警位
                for (int i = 0; i < A_MAP_W.size() && i < 32; i++) {
                    if ((aMap & (1 << i)) != 0) {
                        String alarmName = A_MAP_W.get(i);
                        sendAlarmMessage(clientId, alarmName, "告警");
                        log.warn("NFZX设备告警: clientId={}, alarm={}", clientId, alarmName);
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理NFZX告警数据异常: clientId={}, error={}", clientId, e.getMessage(), e);
        }
    }

    /**
     * 发送告警MQTT消息
     * @param clientId 客户端ID
     * @param alarmName 告警名称
     * @param alarmLevel 告警级别
     */
    private void sendAlarmMessage(String clientId, String alarmName, String alarmLevel) {
        try {
            if (emqxMessageSerice != null && topicsUtils != null) {
                DeviceReportBo deviceReportBo = new DeviceReportBo();
                deviceReportBo.setSerialNumber(clientId);
                deviceReportBo.setServerType(ServerType.MQTT);

                JSONObject alarmData = new JSONObject();
                alarmData.put("alarmName", alarmName);
                alarmData.put("alarmLevel", alarmLevel);
                alarmData.put("timestamp", DateUtils.getNowDate());

                String topic = topicsUtils.buildAlarmTopic(clientId);
                emqxMessageSerice.sendMessage(topic, alarmData.toString());

                log.debug("发送NFZX告警消息: clientId={}, alarm={}, topic={}", clientId, alarmName, topic);
            }
        } catch (Exception e) {
            log.error("发送NFZX告警消息异常: clientId={}, alarm={}, error={}", clientId, alarmName, e.getMessage(), e);
        }
    }

    /**
     * 构建2001命令的完整JSON结构
     * @param sn 设备序列号
     * @return 组装好的JSON对象
     */
    public JSONObject assembly2001(String sn) {
        JSONObject deviceMsg = new JSONObject();

        try {
            // 基本信息
            deviceMsg.put("cmd", 2001);
            deviceMsg.put("sn", sn);
            deviceMsg.put("time", DateUtil.format(new Date(), PURE_DATETIME_FORMAT));

            // 设备配置参数（示例）
            JSONObject config = new JSONObject();
            config.put("reportInterval", 60);  // 上报间隔（秒）
            config.put("heartbeatInterval", 30);  // 心跳间隔（秒）
            config.put("alarmEnabled", true);  // 告警使能

            // 告警阈值配置
            JSONObject alarmThresholds = new JSONObject();
            alarmThresholds.put("voltageHigh", 250.0);  // 电压上限
            alarmThresholds.put("voltageLow", 200.0);   // 电压下限
            alarmThresholds.put("currentHigh", 100.0);  // 电流上限
            alarmThresholds.put("tempHigh", 80.0);      // 温度上限

            deviceMsg.put("config", config);
            deviceMsg.put("alarmThresholds", alarmThresholds);

            log.debug("构建2001命令成功: sn={}", sn);
        } catch (Exception e) {
            log.error("构建2001命令异常: sn={}, error={}", sn, e.getMessage(), e);
        }

        return deviceMsg;
    }
}
