package com.ssac.sip.handler.req.message.response.cmdType;

import com.ssac.sip.domain.SipDevice;
import com.ssac.sip.handler.req.message.IMessageHandler;
import com.ssac.sip.handler.req.message.response.ResponseMessageHandler;
import org.dom4j.Element;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.sip.RequestEvent;

@Component
public class MobilePositionRHandler implements InitializingBean, IMessageHandler {

    @Autowired
    private ResponseMessageHandler responseMessageHandler;
    @Override
    public void handlerCmdType(RequestEvent evt, SipDevice device, Element element) {

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        String cmdType = "MobilePosition";
        responseMessageHandler.addHandler(cmdType, this);
    }
}

