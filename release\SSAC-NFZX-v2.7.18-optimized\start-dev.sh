#!/bin/bash

echo "========================================"
echo " SSAC NFZX IoT Platform v2.7.18"
echo " Database Connection Optimized Version"
echo " Development Environment"
echo "========================================"
echo

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "[ERROR] Java is not installed or not in PATH"
    echo "Please install Java 8+ and add it to PATH"
    exit 1
fi

echo "[INFO] Java environment check passed"
echo "[INFO] Starting SSAC NFZX IoT Platform..."
echo "[INFO] Profile: development"
echo "[INFO] Database: Optimized connection pool configuration"
echo "[INFO] Port: 9901 (HTTP), 9902 (WebSocket)"
echo "[INFO] Monitoring: http://localhost:9901/druid/ (admin/admin)"
echo

# 优化的JVM参数 - 开发环境
JVM_OPTS="-Xms512m -Xmx2048m"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCDetails"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCTimeStamps"
JVM_OPTS="$JVM_OPTS -Xloggc:logs/gc-dev.log"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Duser.timezone=GMT+8"

# 数据库连接优化参数
DB_OPTS="-Ddruid.stat.logSlowSql=true"
DB_OPTS="$DB_OPTS -Ddruid.stat.slowSqlMillis=2000"
DB_OPTS="$DB_OPTS -Dspring.profiles.active=dev"

echo "[INFO] JVM Options: $JVM_OPTS"
echo "[INFO] Database Options: $DB_OPTS"
echo

# 创建日志目录
mkdir -p logs

# 启动应用
java $JVM_OPTS $DB_OPTS -jar ssac-admin-nfzx-v2.7.18-optimized.jar

echo
echo "[INFO] SSAC NFZX IoT Platform stopped"
