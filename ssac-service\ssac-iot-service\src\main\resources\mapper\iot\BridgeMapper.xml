<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.BridgeMapper">

    <resultMap type="Bridge" id="BridgeResult">
        <result property="id"    column="id"    />
        <result property="configJson"    column="config_json"    />
        <result property="name"    column="name"    />
        <result property="enable"    column="enable"    />
        <result property="status"    column="status"    />
        <result property="type"    column="type"    />
        <result property="direction"    column="direction"    />
        <result property="route"    column="route"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectBridgeVo">
        select id, config_json, name, enable, status, type, direction, route, del_flag, create_by, create_time, update_by, update_time, remark from bridge
    </sql>

</mapper>
