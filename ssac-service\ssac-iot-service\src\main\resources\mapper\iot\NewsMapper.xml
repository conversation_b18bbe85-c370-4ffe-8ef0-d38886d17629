<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.NewsMapper">
    
    <resultMap type="com.ssac.iot.domain.News" id="NewsResult">
        <result property="newsId"    column="news_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="isTop"    column="is_top"    />
        <result property="isBanner"    column="is_banner"    />
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="status"    column="status"    />
        <result property="author"    column="author"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectNewsVo">
        select news_id, title, img_url, is_top, is_banner, category_id, category_name, status, author, del_flag, create_by, create_time, update_by, update_time, remark from news
    </sql>

    <select id="selectNewsList" parameterType="com.ssac.iot.domain.News" resultMap="NewsResult">
        select news_id, title, img_url, is_top, is_banner, category_id, category_name, status, author,
               create_by, create_time, update_by, update_time, remark
        from news s
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="isTop != null "> and is_top = #{isTop}</if>
            <if test="isBanner != null "> and is_banner = #{isBanner}</if>
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectTopNewsList" parameterType="com.ssac.iot.domain.News" resultMap="NewsResult">
        select n.news_id, n.title, n.img_url, n.is_top, n.is_banner, n.category_id, c.category_name, n.status, n.author, n.create_time, n.remark
        from news n left join news_category c on c.category_id=n.category_id
        where n.is_top=1 and n.status=1
        order by n.create_time desc
    </select>
    
    <select id="selectNewsByNewsId" parameterType="Long" resultMap="NewsResult">
        select news_id, title, content, img_url, is_top, is_banner, category_id, category_name, status, author, del_flag, create_by, create_time, update_by, update_time, remark from news
        where news_id = #{newsId}
    </select>
        
    <insert id="insertNews" parameterType="com.ssac.iot.domain.News" useGeneratedKeys="true" keyProperty="newsId">
        insert into news
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="imgUrl != null and imgUrl != ''">img_url,</if>
            <if test="isTop != null">is_top,</if>
            <if test="isBanner != null">is_banner,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="status != null">status,</if>
            <if test="author != null and author != ''">author,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="imgUrl != null and imgUrl != ''">#{imgUrl},</if>
            <if test="isTop != null">#{isTop},</if>
            <if test="isBanner != null">#{isBanner},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="status != null">#{status},</if>
            <if test="author != null and author != ''">#{author},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateNews" parameterType="com.ssac.iot.domain.News">
        update news
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="imgUrl != null and imgUrl != ''">img_url = #{imgUrl},</if>
            <if test="isTop != null">is_top = #{isTop},</if>
            <if test="isBanner != null">is_banner = #{isBanner},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="author != null and author != ''">author = #{author},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where news_id = #{newsId}
    </update>

    <delete id="deleteNewsByNewsId" parameterType="Long">
        delete from news where news_id = #{newsId}
    </delete>

    <delete id="deleteNewsByNewsIds" parameterType="String">
        delete from news where news_id in 
        <foreach item="newsId" collection="array" open="(" separator="," close=")">
            #{newsId}
        </foreach>
    </delete>
</mapper>