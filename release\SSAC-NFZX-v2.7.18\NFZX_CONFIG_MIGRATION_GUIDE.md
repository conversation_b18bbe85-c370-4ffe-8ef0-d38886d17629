# 🔧 NFZX环境配置迁移完成指南

## 📋 配置迁移概览

成功将2.4版本的NFZX项目环境配置完整迁移到当前Spring Boot 2.7.18版本的SSAC智能控制平台中。

### 🎯 迁移目标
- ✅ **数据库配置**: 迁移NFZX专用数据库连接配置
- ✅ **Redis配置**: 应用NFZX环境的Redis连接参数
- ✅ **MQTT配置**: 迁移NFZX的MQTT服务器配置
- ✅ **服务端口**: 应用NFZX项目的端口配置
- ✅ **业务配置**: 迁移规则引擎、SIP等业务配置

## 🔧 配置迁移详情

### 1. 主配置文件 (application.yml)

#### 📁 项目基础配置
```yaml
ssac:
  name: ssac
  version: 3.8.5
  profile: F:/display/byciot/scala/uploadPath  # 更新为NFZX文件路径
```

#### 🌐 服务端口配置
```yaml
server:
  port: 9901                # 从8080更新为9901 (NFZX端口)
  broker:
    broker-node: node1      # 新增集群节点配置
    openws: false           # 新增WebSocket控制开关
    websocket-port: 9902    # 从8083更新为9902
  tcp:
    enabled: false          # 从true更新为false (NFZX默认关闭TCP)
```

#### ⚙️ 设备平台配置
```yaml
server:
  device:
    platform:
      expried: 120          # 新增设备离线判断时间
```

#### 🔗 集群配置
```yaml
cluster:
  enable: true              # 新增集群配置
  type: redis
```

#### 📊 Redisson配置优化
```yaml
redisson:
  keyPrefix:                # 清空key前缀
  threads: 4                # 从16降低为4
  nettyThreads: 8           # 从32降低为8
  singleServerConfig:
    connectionMinimumIdleSize: 8    # 从32降低为8
    connectionPoolSize: 32          # 从64降低为32
```

### 2. 开发环境配置 (application-dev.yml)

#### 🗄️ 数据库配置
```yaml
spring:
  datasource:
    dynamic:
      datasource:
        master:
          url: ***************************************?...
          username: root
          password: NFZX505050
        taos:
          enabled: false    # 暂时禁用TDengine
```

#### 🔴 Redis配置
```yaml
spring:
  redis:
    host: *************     # NFZX Redis服务器
    password: 'cceste660'   # NFZX Redis密码
    database: 3             # 数据库索引
```

#### 📡 MQTT配置
```yaml
spring:
  mqtt:
    username: admin         # NFZX MQTT用户名
    password: NFZX@2024     # NFZX MQTT密码
    host-url: tcp://*************:1883  # NFZX MQTT服务器
    client-id: 1111111      # 固定客户端ID
```

#### 🔧 新增配置
- **Redisson配置**: 完整的Redis分布式配置
- **LiteFlow配置**: 规则引擎配置
- **日志优化**: 调整日志级别为生产环境适用

### 3. 生产环境配置 (application-prod.yml)

#### 🗄️ 数据库配置
```yaml
spring:
  datasource:
    dynamic:
      datasource:
        master:
          url: ***********************************?...
          username: root
          password: NFZX505050
        taos:
          enabled: false    # 生产环境暂时禁用TDengine
```

#### 🔴 Redis配置
```yaml
spring:
  redis:
    host: 127.0.0.1         # 生产环境本地Redis
    password: 'cceste660'   # NFZX Redis密码
    database: 3             # 数据库索引
```

#### 📡 MQTT配置
```yaml
spring:
  mqtt:
    username: admin         # NFZX MQTT用户名
    password: NFZX@2024     # NFZX MQTT密码
    host-url: tcp://*************:1883  # NFZX MQTT服务器
    client-id: 1111111      # 固定客户端ID
```

## 🔍 配置对比分析

### 📊 主要变更对比

| 配置项 | 原配置 | NFZX配置 | 说明 |
|--------|--------|----------|------|
| 服务端口 | 8080 | 9901 | NFZX专用端口 |
| WebSocket端口 | 8083 | 9902 | NFZX WebSocket端口 |
| 文件路径 | D:/uploadPath | F:/display/byciot/scala/uploadPath | NFZX文件存储路径 |
| 数据库 | fastbee/ssac | nfzxiot | NFZX专用数据库 |
| Redis主机 | localhost/********** | ************* | NFZX Redis服务器 |
| MQTT主机 | localhost/********** | ************* | NFZX MQTT服务器 |
| MQTT用户 | fastbee/ssac | admin | NFZX MQTT认证 |
| MQTT密码 | fastbee/ssac | NFZX@2024 | NFZX MQTT密码 |

### 🆕 新增配置

1. **集群配置**: 支持Redis集群模式
2. **设备平台配置**: 设备离线判断时间
3. **Broker节点配置**: MQTT Broker集群节点
4. **LiteFlow规则引擎**: 完整的规则引擎配置
5. **Redisson优化**: 针对NFZX环境的连接池优化

## 🧪 验证测试

### ✅ 编译验证
```bash
# 编译成功
mvn clean compile

# 打包成功  
mvn clean package -DskipTests
```

### 🔧 配置验证清单

#### 开发环境验证
- [ ] 数据库连接: `*************:3306/nfzxiot`
- [ ] Redis连接: `*************:6379` (密码: cceste660)
- [ ] MQTT连接: `tcp://*************:1883` (admin/NFZX@2024)
- [ ] 服务启动: 端口9901
- [ ] WebSocket: 端口9902

#### 生产环境验证
- [ ] 数据库连接: `127.0.0.1:3306/nfzxiot`
- [ ] Redis连接: `127.0.0.1:6379` (密码: cceste660)
- [ ] MQTT连接: `tcp://*************:1883` (admin/NFZX@2024)
- [ ] 服务启动: 端口9901
- [ ] 日志级别: info

## 📝 使用说明

### 🚀 启动应用

#### 开发环境启动
```bash
# 使用开发环境配置启动
java -jar ssac-admin.jar --spring.profiles.active=dev
```

#### 生产环境启动
```bash
# 使用生产环境配置启动
java -jar ssac-admin.jar --spring.profiles.active=prod
```

### 🔧 配置调整建议

1. **数据库优化**
   - 根据实际负载调整连接池大小
   - 考虑启用TDengine时序数据库
   - 配置读写分离（如需要）

2. **Redis优化**
   - 根据内存使用情况调整连接池
   - 考虑Redis集群配置
   - 优化缓存策略

3. **MQTT优化**
   - 根据设备数量调整连接参数
   - 配置MQTT集群（如需要）
   - 优化消息队列大小

## ⚠️ 注意事项

### 🔒 安全配置
- **密码安全**: 生产环境请修改默认密码
- **网络安全**: 确保服务器防火墙配置正确
- **访问控制**: 配置适当的IP白名单

### 📊 性能考虑
- **连接池**: 根据并发量调整数据库和Redis连接池
- **内存配置**: 根据设备数量调整JVM内存参数
- **日志级别**: 生产环境建议使用info级别

### 🔄 兼容性
- **Spring Boot版本**: 确保与2.7.18版本兼容
- **数据库版本**: 确保MySQL版本兼容
- **Redis版本**: 确保Redis版本支持所有功能

## 🎉 迁移完成

### ✅ 迁移成果
- **完整配置迁移**: 所有NFZX环境配置已成功迁移
- **多环境支持**: 开发和生产环境配置完整
- **功能保持**: 保持原有功能的同时优化性能
- **文档完善**: 提供详细的配置说明和使用指南

### 🚀 后续工作
1. 根据实际环境调整IP地址和端口
2. 配置SSL证书（如需要）
3. 设置监控和日志收集
4. 进行压力测试和性能调优

---
**迁移日期**: 2025-01-26  
**源版本**: FastBee 2.4.0 NFZX  
**目标版本**: SSAC Spring Boot 2.7.18  
**迁移状态**: ✅ 完成  
**文档版本**: v1.0
