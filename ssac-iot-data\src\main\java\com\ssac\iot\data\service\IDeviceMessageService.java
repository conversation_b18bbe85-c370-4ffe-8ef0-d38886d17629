package com.ssac.iot.data.service;

import com.ssac.common.core.mq.message.DeviceMessage;
import com.ssac.common.core.thingsModel.ThingsModelSimpleItem;
import com.ssac.iot.model.VariableReadVO;
import com.ssac.modbus.model.ModbusRtu;

import java.util.List;

/**
 * 设备消息Service接口
 */
public interface IDeviceMessageService {

    void messagePost(DeviceMessage deviceMessage);

    String messageEncode(ModbusRtu modbusRtu);

    List<ThingsModelSimpleItem> messageDecode(DeviceMessage deviceMessage);


    /**
     * 变量读取
     * @param readVO
     */
    public void readVariableValue(VariableReadVO readVO);
}
