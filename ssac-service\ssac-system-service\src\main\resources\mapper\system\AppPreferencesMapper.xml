<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.system.mapper.AppPreferencesMapper">
    
    <resultMap type="com.ssac.system.domain.AppPreferences" id="AppPreferencesResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="language"    column="language"    />
        <result property="timeZone"    column="time_zone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAppPreferencesVo">
        select id, user_id, language, time_zone, create_by, create_time, update_by, update_time, remark from app_preferences
    </sql>

    <select id="selectAppPreferencesList" parameterType="com.ssac.system.domain.AppPreferences" resultMap="AppPreferencesResult">
        <include refid="selectAppPreferencesVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="language != null  and language != ''"> and `language` = #{language}</if>
            <if test="timeZone != null  and timeZone != ''"> and time_zone = #{timeZone}</if>
        </where>
    </select>
    
    <select id="selectAppPreferencesById" parameterType="Long" resultMap="AppPreferencesResult">
        <include refid="selectAppPreferencesVo"/>
        where id = #{id}
    </select>

    <select id="selectAppPreferencesByUserId" parameterType="Long" resultMap="AppPreferencesResult">
        <include refid="selectAppPreferencesVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertAppPreferences" parameterType="com.ssac.system.domain.AppPreferences">
        insert into app_preferences
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="language != null">`language`,</if>
            <if test="timeZone != null">time_zone,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="language != null">#{language},</if>
            <if test="timeZone != null">#{timeZone},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAppPreferences" parameterType="com.ssac.system.domain.AppPreferences">
        update app_preferences
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="language != null">`language` = #{language},</if>
            <if test="timeZone != null">time_zone = #{timeZone},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppPreferencesByUserId" parameterType="Long">
        delete from app_preferences where user_id = #{userId}
    </delete>

    <delete id="deleteAppPreferencesByUserIds" parameterType="String">
        delete from app_preferences where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>
