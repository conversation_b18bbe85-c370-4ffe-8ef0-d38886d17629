# 📊 数据库连接监控配置指南

## 🎯 监控目标

通过Druid监控面板实时监控数据库连接状态，及时发现和解决连接超时问题。

## 🔧 监控配置

### 1. **Druid监控面板访问**

#### 开发环境
- **URL**: http://localhost:9901/druid/
- **用户名**: admin
- **密码**: admin

#### 生产环境  
- **URL**: http://localhost:9901/druid/
- **用户名**: admin
- **密码**: druid@2024

### 2. **关键监控指标**

#### 连接池状态
- **活跃连接数**: 当前正在使用的连接数
- **空闲连接数**: 连接池中空闲的连接数
- **等待线程数**: 等待获取连接的线程数
- **连接池使用率**: 活跃连接数/最大连接数

#### 性能指标
- **平均获取连接时间**: 获取连接的平均耗时
- **最大获取连接时间**: 获取连接的最大耗时
- **连接泄漏检测**: 未正确关闭的连接
- **废弃连接统计**: 超时被回收的连接

#### SQL执行统计
- **慢SQL统计**: 执行时间超过阈值的SQL
- **SQL执行次数**: 各SQL的执行频率
- **SQL平均执行时间**: 各SQL的平均耗时
- **SQL最大执行时间**: 各SQL的最大耗时

## 📈 告警阈值设置

### 连接池告警
```yaml
# 建议告警阈值
连接池使用率 > 80%        # 高使用率告警
等待线程数 > 10           # 连接等待告警
平均获取连接时间 > 1000ms  # 获取连接慢告警
连接泄漏数量 > 5          # 连接泄漏告警
```

### SQL性能告警
```yaml
# 建议告警阈值
SQL执行时间 > 2000ms      # 慢SQL告警
SQL错误率 > 1%           # SQL错误率告警
并发执行数 > 50          # 高并发告警
```

## 🔍 问题排查步骤

### 1. **连接超时问题排查**

#### 步骤1: 检查连接池状态
```
访问 /druid/datasource.html
查看：
- 连接池使用率是否过高
- 是否有等待获取连接的线程
- 连接获取时间是否异常
```

#### 步骤2: 检查慢SQL
```
访问 /druid/sql.html
查看：
- 是否有执行时间过长的SQL
- 是否有频繁执行的SQL
- 是否有锁等待的SQL
```

#### 步骤3: 检查连接泄漏
```
访问 /druid/datasource.html
查看：
- ActiveCount 是否持续增长
- 是否有RemoveAbandoned记录
- 连接创建和销毁是否平衡
```

### 2. **性能优化建议**

#### 连接池优化
```yaml
# 根据监控数据调整
initial-size: 根据平均连接数设置
max-active: 根据峰值连接数设置  
max-wait: 根据业务容忍度设置
```

#### SQL优化
```sql
-- 针对慢SQL进行优化
1. 添加合适的索引
2. 优化查询条件
3. 分页查询大数据量
4. 避免全表扫描
```

## 📊 监控报表

### 1. **连接池健康度报表**
- 连接池使用率趋势图
- 连接获取时间分布图
- 连接泄漏统计图
- 错误连接统计图

### 2. **SQL性能报表**
- 慢SQL Top 10
- SQL执行频率排行
- SQL错误率统计
- 并发执行统计

## 🚨 常见问题解决

### 问题1: 连接池耗尽
```
现象：获取连接超时，前端页面响应慢
原因：连接未正确释放或并发量过大
解决：
1. 检查代码是否正确关闭连接
2. 增加连接池大小
3. 减少连接获取等待时间
```

### 问题2: 慢SQL导致连接占用
```
现象：连接长时间被占用，其他请求等待
原因：SQL执行时间过长
解决：
1. 优化慢SQL语句
2. 添加SQL执行超时设置
3. 分页处理大数据量查询
```

### 问题3: 数据库服务器连接数限制
```
现象：数据库拒绝新连接
原因：数据库max_connections设置过小
解决：
1. 增加数据库max_connections
2. 优化应用连接池配置
3. 实施连接池监控
```

## 📝 日常维护检查清单

### 每日检查
- [ ] 连接池使用率是否正常
- [ ] 是否有新的慢SQL
- [ ] 连接获取时间是否异常
- [ ] 是否有连接泄漏

### 每周检查  
- [ ] 连接池配置是否需要调整
- [ ] SQL性能是否有退化
- [ ] 数据库连接数趋势分析
- [ ] 错误日志分析

### 每月检查
- [ ] 连接池配置优化
- [ ] 数据库性能调优
- [ ] 监控告警阈值调整
- [ ] 容量规划评估

## 🎯 优化效果预期

实施监控和优化后，预期效果：

### 性能提升
- ✅ 连接获取时间 < 100ms
- ✅ SQL执行时间 < 1000ms  
- ✅ 连接池使用率 < 70%
- ✅ 前端响应时间 < 2秒

### 稳定性提升
- ✅ 连接超时错误 < 0.1%
- ✅ 连接泄漏数量 = 0
- ✅ 数据库连接稳定性 > 99.9%
- ✅ 系统可用性 > 99.95%

---
**监控配置完成后，建议定期查看监控数据，及时发现和解决问题！**
