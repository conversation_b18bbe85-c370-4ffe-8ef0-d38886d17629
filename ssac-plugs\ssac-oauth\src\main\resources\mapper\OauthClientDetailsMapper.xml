<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fastbee.oauth.mapper.OauthClientDetailsMapper">

    <resultMap type="com.fastbee.oauth.domain.OauthClientDetails" id="OauthClientDetailsResult">
        <result property="id"    column="id"    />
        <result property="clientId"    column="client_id"    />
        <result property="resourceIds"    column="resource_ids"    />
        <result property="clientSecret"    column="client_secret"    />
        <result property="scope"    column="scope"    />
        <result property="authorizedGrantTypes"    column="authorized_grant_types"    />
        <result property="webServerRedirectUri"    column="web_server_redirect_uri"    />
        <result property="authorities"    column="authorities"    />
        <result property="accessTokenValidity"    column="access_token_validity"    />
        <result property="refreshTokenValidity"    column="refresh_token_validity"    />
        <result property="additionalInformation"    column="additional_information"    />
        <result property="autoapprove"    column="autoapprove"    />
        <result property="type"    column="type"    />
        <result property="status"    column="status"    />
        <result property="icon"    column="icon"    />
        <result property="cloudSkillId"    column="cloud_skill_id"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="tenantName"    column="tenant_name"    />
    </resultMap>

    <sql id="selectOauthClientDetailsVo">
        select id, client_id, resource_ids, client_secret, scope, authorized_grant_types, web_server_redirect_uri, authorities, access_token_validity, refresh_token_validity, additional_information, autoapprove, type, status, icon, cloud_skill_id,tenant_id, tenant_name from oauth_client_details
    </sql>

    <select id="selectOauthClientDetailsList" parameterType="com.fastbee.oauth.domain.OauthClientDetails" resultMap="OauthClientDetailsResult">
        <include refid="selectOauthClientDetailsVo"/>
        <where>
            <if test="clientId != null  and clientId != ''"> and client_id = #{clientId}</if>
            <if test="authorizedGrantTypes != null  and authorizedGrantTypes != ''"> and authorized_grant_types = #{authorizedGrantTypes}</if>
            <if test="autoapprove != null  and autoapprove != ''"> and autoapprove = #{autoapprove}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
        </where>
    </select>

    <select id="selectOauthClientDetailsById" parameterType="Long" resultMap="OauthClientDetailsResult">
        <include refid="selectOauthClientDetailsVo"/>
        where id = #{id}
    </select>

    <select id="selectOauthClientDetailsByType" resultType="com.fastbee.oauth.domain.OauthClientDetails">
        <include refid="selectOauthClientDetailsVo"/>
        where type = #{type}
        and tenant_id = #{tenantId}
    </select>

    <select id="selectOauthClientDetailsByClientId" resultType="com.fastbee.oauth.domain.OauthClientDetails">
        <include refid="selectOauthClientDetailsVo"/>
        where client_id = #{clientId}
    </select>

    <insert id="insertOauthClientDetails" parameterType="com.fastbee.oauth.domain.OauthClientDetails">
        insert into oauth_client_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clientId != null">client_id,</if>
            <if test="resourceIds != null">resource_ids,</if>
            <if test="clientSecret != null">client_secret,</if>
            <if test="scope != null">scope,</if>
            <if test="authorizedGrantTypes != null">authorized_grant_types,</if>
            <if test="webServerRedirectUri != null">web_server_redirect_uri,</if>
            <if test="authorities != null">authorities,</if>
            <if test="accessTokenValidity != null">access_token_validity,</if>
            <if test="refreshTokenValidity != null">refresh_token_validity,</if>
            <if test="additionalInformation != null">additional_information,</if>
            <if test="autoapprove != null">autoapprove,</if>
            <if test="type != null">type,</if>
            <if test="status != null">status,</if>
            <if test="icon != null">icon,</if>
            <if test="cloudSkillId != null">cloud_skill_id,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="tenantName != null and tenantName != ''">tenant_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clientId != null">#{clientId},</if>
            <if test="resourceIds != null">#{resourceIds},</if>
            <if test="clientSecret != null">#{clientSecret},</if>
            <if test="scope != null">#{scope},</if>
            <if test="authorizedGrantTypes != null">#{authorizedGrantTypes},</if>
            <if test="webServerRedirectUri != null">#{webServerRedirectUri},</if>
            <if test="authorities != null">#{authorities},</if>
            <if test="accessTokenValidity != null">#{accessTokenValidity},</if>
            <if test="refreshTokenValidity != null">#{refreshTokenValidity},</if>
            <if test="additionalInformation != null">#{additionalInformation},</if>
            <if test="autoapprove != null">#{autoapprove},</if>
            <if test="type != null">#{type},</if>
            <if test="status != null">#{status},</if>
            <if test="icon != null">#{icon},</if>
            <if test="cloudSkillId != null">#{cloudSkillId},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="tenantName != null and tenantName != ''">#{tenantName},</if>
         </trim>
    </insert>

    <update id="updateOauthClientDetails" parameterType="com.fastbee.oauth.domain.OauthClientDetails">
        update oauth_client_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="resourceIds != null">resource_ids = #{resourceIds},</if>
            <if test="clientSecret != null">client_secret = #{clientSecret},</if>
            <if test="scope != null">scope = #{scope},</if>
            <if test="authorizedGrantTypes != null">authorized_grant_types = #{authorizedGrantTypes},</if>
            <if test="webServerRedirectUri != null">web_server_redirect_uri = #{webServerRedirectUri},</if>
            <if test="authorities != null">authorities = #{authorities},</if>
<!--            <if test="accessTokenValidity != null">access_token_validity = #{accessTokenValidity},</if>-->
<!--            <if test="refreshTokenValidity != null">refresh_token_validity = #{refreshTokenValidity},</if>-->
            access_token_validity = #{accessTokenValidity},
            refresh_token_validity = #{refreshTokenValidity},
            <if test="additionalInformation != null">additional_information = #{additionalInformation},</if>
            <if test="autoapprove != null">autoapprove = #{autoapprove},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="cloudSkillId != null">cloud_skill_id = #{cloudSkillId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOauthClientDetailsByClientId" parameterType="String">
        delete from oauth_client_details where client_id = #{clientId}
    </delete>

    <delete id="deleteOauthClientDetailsByIds" parameterType="Long">
        delete from oauth_client_details where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
