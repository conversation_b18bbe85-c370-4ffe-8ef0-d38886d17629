package com.fastbee.common.constant;

/**
 *
 * <AUTHOR>
 * @date 2023-08-03 10:20
 */
public class ProductAuthConstant {

    /**
     * 产品设备认证方式-简单认证
     */
    public static final Integer AUTH_WAY_SIMPLE = 1;
    /**
     * 产品设备认证方式-简单认证
     */
    public static final Integer AUTH_WAY_ENCRYPT = 2;
    /**
     * 产品设备认证方式-简单认证
     */
    public static final Integer AUTH_WAY_SIMPLE_AND_ENCRYPT = 3;

    /**
     * 产品设备客户端ID认证类型-简单认证
     */
    public static final String CLIENT_ID_AUTH_TYPE_SIMPLE = "S";

    /**
     * 产品设备客户端ID认证类型-简单认证
     */
    public static final String CLIENT_ID_AUTH_TYPE_ENCRYPT = "E";
    /**
     * 设备授权
     */
    public static final Integer AUTHORIZE = 1;
    /**
     * 设备没有授权
     */
    public static final Integer NO_AUTHORIZE = 1;

}
