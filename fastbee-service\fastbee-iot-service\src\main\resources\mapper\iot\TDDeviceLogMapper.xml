<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.TDDeviceLogMapper">

    <resultMap type="com.ssac.iot.model.MonitorModel" id="MonitorResult">
        <result property="value"    column="log_value"    />
        <result property="time"    column="ts"    />
    </resultMap>

    <resultMap type="com.ssac.iot.domain.DeviceLog" id="DeviceLogResult">
        <result property="logType"    column="log_type"    />
        <result property="logValue"    column="log_value"    />
        <result property="mode"    column="mode"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="identity"    column="identity"    />
        <result property="createBy"    column="create_by"    />
        <result property="isMonitor"    column="is_monitor"    />
        <result property="createTime"    column="ts"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="tenantName"    column="tenant_name"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap type="com.ssac.iot.model.HistoryModel" id="HistoryResult">
        <result property="value"    column="log_value"    />
        <result property="time"    column="ts"    />
        <result property="identity" column="identity" />
    </resultMap>

    <update id="createDB">
        create database if not exists  ${database} vgroups 4;
    </update>

    <update id="createSTable">
        create STABLE  if not exists ${database}.device_log
        (ts timestamp,
        `log_value` BINARY(100),
        is_monitor TINYINT,
        log_type TINYINT,
        `identity` BINARY(100),
        mode TINYINT,
        remark BINARY(500))
        TAGS(serial_number BINARY(50));
    </update>

    <insert id="save" parameterType="com.ssac.iot.domain.DeviceLog" useGeneratedKeys="false">
        insert into ${database}.device_${device.serialNumber} using device_log
        tags (#{device.serialNumber})
        values (now,
            #{device.logValue},
            #{device.isMonitor},
            #{device.logType},
            #{device.identity},
            #{device.mode},
            #{device.remark});
    </insert>

    <insert id="saveBatch" parameterType="com.ssac.iot.tdengine.service.model.TdLogDto" useGeneratedKeys="false">
        insert into ${database}.device_${data.serialNumber} using device_log
        tags (#{data.serialNumber})
        values
        <foreach collection="data.list" separator=" " item="device" index="index">
            (now,
            #{device.logValue},
            #{device.isMonitor},
            #{device.logType},
            #{device.identity},
            #{device.mode},
            #{device.remark})
        </foreach>
    </insert>

    <delete id="deleteDeviceLogByDeviceNumber" parameterType="com.ssac.iot.domain.DeviceLog">
        DROP TABLE IF EXISTS ${database}.device_${serialNumber};
    </delete>

    <select id="selectPropertyLogCount" parameterType="com.ssac.iot.domain.Device" resultType="Long">
        select count(mode) as propertyCount
        from ${database}.device_log
        where log_type=1
    </select>

    <select id="selectFunctionLogCount" parameterType="com.ssac.iot.domain.Device" resultType="Long">
        select count(mode) as functionCount
        from ${database}.device_log
        where log_type=2
    </select>

    <select id="selectEventLogCount" parameterType="com.ssac.iot.domain.Device" resultType="Long">
        select count(mode) as eventCount
        from ${database}.device_log
        where log_type=3
    </select>

    <select id="selectMonitorLogCount" parameterType="com.ssac.iot.domain.Device" resultType="Long">
        select count(mode) as monitorCount
        from ${database}.device_log
        where log_type=1 and is_monitor=1
    </select>

    <select id="selectMonitorList" parameterType="com.ssac.iot.domain.DeviceLog" resultMap="MonitorResult">
        select log_value, ts from ${database}.device_log
        <where>
             is_monitor=1
            <if test="device.serialNumber != null and device.serialNumber !=''"> and serial_number = #{device.serialNumber}</if>
            <if test="device.identity != null  and device.identity != ''"> and identity like  #{device.identity}</if>
            <if test="device.beginTime != null and device.beginTime != '' and device.endTime != null and device.endTime != ''"> and ts between #{device.beginTime} and #{device.endTime}</if>
        order by ts desc
        limit #{device.total}
        </where>
    </select>

    <select id="selectDeviceLogList" parameterType="com.ssac.iot.domain.DeviceLog" resultMap="DeviceLogResult">
        select * from ${database}.device_log
        <where>
            <if test="device.isMonitor != null"> and is_monitor = #{device.isMonitor}</if>
            <if test="device.serialNumber != null and device.serialNumber !=''"> and serial_number = #{device.serialNumber}</if>
            <if test="device.logType != null "> and log_type = #{device.logType}</if>
            <if test="device.logType == null "> and log_type != 7</if>
            <if test="device.identity != null  and device.identity != ''"> and identity like #{device.identity}</if>
        </where>
        order by ts desc
    </select>

    <select id="selectHistoryList" parameterType="com.ssac.iot.domain.DeviceLog" resultMap="HistoryResult">
        select log_value,
               ts,
               identity
        from ${database}.device_log
        <where>
        <if test="device.beginTime != null and device.beginTime != '' and device.endTime != null and device.endTime != ''">
            and ts between #{device.beginTime} and #{device.endTime}
        </if>
            <if test="device.serialNumber != null and device.serialNumber !=''"> and serial_number = #{device.serialNumber}</if>
        </where>
        order by ts desc
    </select>

    <select id="listHistory" parameterType="com.ssac.iot.domain.DeviceLog" resultMap="HistoryResult">
        select log_value,
        ts,
        identity
        from ${database}.device_log
        <where>
            <if test="device.beginTime != null and device.beginTime != '' and device.endTime != null and device.endTime != ''">
                and ts between #{device.beginTime} and #{device.endTime}
            </if>
            <if test="device.serialNumber != null and device.serialNumber !=''">
                and serial_number = #{device.serialNumber}
            </if>
            <if test="device.identityList != null and device.identityList.size > 0">
                and identity in
                <foreach collection="device.identityList" item="identity" open="(" separator="," close=")">
                    #{identity}
                </foreach>
            </if>
            <if test="device.logType != null">
                and log_type = #{device.logType}
            </if>
        </where>
        order by ts desc
    </select>

    <select id="listhistoryGroupByCreateTime" resultType="com.ssac.iot.model.HistoryModel">
        select ts as time,  identity, log_value as value
        from ${database}.device_log
        <where>
            <if test="device.beginTime != null and device.beginTime != '' and device.endTime != null and device.endTime != ''">
                and ts between #{device.beginTime} and #{device.endTime}
            </if>
            <if test="device.serialNumber != null and device.serialNumber !=''"> and serial_number = #{device.serialNumber}</if>
        </where>
        order by ts desc
    </select>

    <select id="selectStatsValue" resultType="java.lang.String">
        select log_value
        from ${database}.device_log
        <where>
            <if test="device.logType != null">
                and log_type = #{device.logType}
            </if>
            <if test="device.beginTime != null and device.beginTime != '' and device.endTime != null and device.endTime != ''">
                and ts between #{device.beginTime} and #{device.endTime}
            </if>
            <if test="device.identity != null and device.identity != ''">
                and identity = #{device.identity}
            </if>
            <if test="device.serialNumber != null and device.serialNumber != ''">
                and serial_number = #{device.serialNumber}
            </if>
        </where>
    </select>

    <select id="countThingsModelInvoke" resultType="com.ssac.iot.model.ThingsModelLogCountVO">
        select identity identifier, count(identity) `count`
        from ${database}.device_log
        where log_type = 2
        <if test="dataCenterParam.serialNumber != null and dataCenterParam.serialNumber != ''">
            and serial_number = #{dataCenterParam.serialNumber}
        </if>
        <if test="dataCenterParam.beginTime != null and dataCenterParam.beginTime != '' and dataCenterParam.endTime != null and dataCenterParam.endTime != ''">
            and ts between #{dataCenterParam.beginTime} and #{dataCenterParam.endTime}
        </if>
        group by identity
    </select>
</mapper>

