<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ssac</groupId>
        <artifactId>ssac-notify</artifactId>
        <version>3.8.5</version>
    </parent>

    <description>通知核心发送模块</description>
    <artifactId>ssac-notify-core</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-common</artifactId>
        </dependency>
        <!-- 通知配置 -->
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-notify-web</artifactId>
        </dependency>
        <!-- 微信 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-iot-service</artifactId>
        </dependency>
        <!-- 阿里云语�?-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dyvmsapi20170525</artifactId>
            <version>2.1.4</version>
        </dependency>
        <!-- 腾讯云语�?-->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <version>3.1.952</version>
        </dependency>
        <!-- 邮箱快捷�?-->
        <dependency>
            <groupId>org.dromara.sms4j</groupId>
            <artifactId>sms4j-Email-core</artifactId>
            <version>3.1.0</version>
        </dependency>
        <!-- 钉钉官方�?-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>1.1.32</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>2.0.0</version>
        </dependency>
    </dependencies>

</project>
