package com.ssac.sip.service;

import com.ssac.common.core.thingsModel.ThingsModelSimpleItem;
import com.ssac.sip.domain.SipDevice;
import com.ssac.sip.domain.SipDeviceChannel;
import com.ssac.sip.model.RecordList;
import com.ssac.sip.server.msg.Alarm;

import java.util.List;

public interface IMqttService {
    void publishInfo(SipDevice device);
    void publishStatus(SipDevice device, int deviceStatus);
    void publishEvent(Alarm alarm);
    void publishProperty(Long productId, String deviceNum, List<ThingsModelSimpleItem> thingsList, int delay);
    void publishChannelsProperty(String DeviceSipId, List<SipDeviceChannel> channels);
    void publishRecordsProperty(String DeviceSipId, RecordList recordList);
}

