@echo off
title SSAC NFZX IoT Platform v2.7.18 - Production Environment (Database Optimized)

echo.
echo ========================================
echo  SSAC NFZX IoT Platform v2.7.18
echo  Database Connection Optimized Version
echo  Production Environment
echo ========================================
echo.

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java is not installed or not in PATH
    echo Please install Java 8+ and add it to PATH
    pause
    exit /b 1
)

echo [INFO] Java environment check passed
echo [INFO] Starting SSAC NFZX IoT Platform...
echo [INFO] Profile: production
echo [INFO] Database: High-performance optimized connection pool
echo [INFO] Port: 9901 (HTTP), 9902 (WebSocket)
echo [INFO] Monitoring: http://localhost:9901/druid/ (admin/druid@2024)
echo.

REM 生产环境优化的JVM参数
set JVM_OPTS=-Xms1024m -Xmx4096m
set JVM_OPTS=%JVM_OPTS% -XX:+UseG1GC
set JVM_OPTS=%JVM_OPTS% -XX:MaxGCPauseMillis=200
set JVM_OPTS=%JVM_OPTS% -XX:+UseStringDeduplication
set JVM_OPTS=%JVM_OPTS% -XX:+OptimizeStringConcat
set JVM_OPTS=%JVM_OPTS% -XX:+UseCompressedOops
set JVM_OPTS=%JVM_OPTS% -XX:+UseCompressedClassPointers
set JVM_OPTS=%JVM_OPTS% -Xloggc:logs/gc-prod.log
set JVM_OPTS=%JVM_OPTS% -XX:+PrintGCDetails
set JVM_OPTS=%JVM_OPTS% -XX:+PrintGCTimeStamps
set JVM_OPTS=%JVM_OPTS% -XX:+UseGCLogFileRotation
set JVM_OPTS=%JVM_OPTS% -XX:NumberOfGCLogFiles=5
set JVM_OPTS=%JVM_OPTS% -XX:GCLogFileSize=10M
set JVM_OPTS=%JVM_OPTS% -Dfile.encoding=UTF-8
set JVM_OPTS=%JVM_OPTS% -Duser.timezone=GMT+8

REM 生产环境数据库连接优化参数
set DB_OPTS=-Ddruid.stat.logSlowSql=true
set DB_OPTS=%DB_OPTS% -Ddruid.stat.slowSqlMillis=1000
set DB_OPTS=%DB_OPTS% -Ddruid.stat.mergeSql=true
set DB_OPTS=%DB_OPTS% -Dspring.profiles.active=prod

REM 性能监控参数
set MONITOR_OPTS=-Dmanagement.endpoints.web.exposure.include=health,info,metrics,druid
set MONITOR_OPTS=%MONITOR_OPTS% -Dmanagement.endpoint.health.show-details=always

echo [INFO] JVM Options: %JVM_OPTS%
echo [INFO] Database Options: %DB_OPTS%
echo [INFO] Monitor Options: %MONITOR_OPTS%
echo.

REM 创建日志目录
if not exist logs mkdir logs

REM 启动应用
java %JVM_OPTS% %DB_OPTS% %MONITOR_OPTS% -jar ssac-admin-nfzx-v2.7.18-optimized.jar

echo.
echo [INFO] SSAC NFZX IoT Platform stopped
pause
