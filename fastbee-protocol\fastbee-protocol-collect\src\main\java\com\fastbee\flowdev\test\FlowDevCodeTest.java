package com.ssac.flowdev.test;

import com.ssac.common.core.mq.message.DeviceData;
import com.ssac.flowdev.codec.FlowDevDecoder;
import com.ssac.flowdev.codec.FlowDevEncoder;
import com.ssac.flowdev.model.FlowDev;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.buffer.Unpooled;

/**
 * <AUTHOR>
 */
public class FlowDevCodeTest {

    private static FlowDevDecoder decoder = new FlowDevDecoder("com.ssac");
    private static FlowDevEncoder encoder = new FlowDevEncoder("com.ssac");


    public static void main(String[] args) {

        String flowData = "681B68B33701120008C100000000000000000000022050004341231811215716";
        ByteBuf in = Unpooled.wrappedBuffer(ByteBufUtil.decodeHexDump(flowData));
        DeviceData data = DeviceData.builder()
                .buf(in).build();
        FlowDev flowDev = decoder.decode(data, null);
        System.out.println(flowDev);

    }
}

