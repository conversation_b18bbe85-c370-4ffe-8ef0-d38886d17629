#!/bin/bash

echo "========================================"
echo "  SSAC NFZX IoT Platform v2.7.18"
echo "  Starting Production Environment..."
echo "========================================"

# 设置JVM参数 - 生产环境优化
JAVA_OPTS="-Xms1024m -Xmx4096m -XX:+UseG1GC -XX:+UseStringDeduplication -XX:MaxGCPauseMillis=200 -XX:+DisableExplicitGC"

# 启动应用 - 生产环境
java $JAVA_OPTS -jar ssac-admin-nfzx-v2.7.18.jar --spring.profiles.active=prod
