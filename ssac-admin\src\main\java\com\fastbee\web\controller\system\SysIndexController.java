package com.ssac.web.controller.system;

import com.ssac.common.utils.MessageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ssac.common.config.RuoYiConfig;
import com.ssac.common.utils.StringUtils;

/**
 * 首页
 *
 * <AUTHOR>
 */
@RestController
public class SysIndexController
{
    /** 系统基础配置 */
    @Autowired
    private RuoYiConfig ruoyiConfig;

    /**
     * 访问首页，提示语
     */
    @RequestMapping("/")
    public String index()
    {
        return StringUtils.format(MessageUtils.message("index.welcome.message"), ruoyiConfig.getName(), ruoyiConfig.getVersion());
    }
}

