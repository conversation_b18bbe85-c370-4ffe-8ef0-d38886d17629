#!/bin/bash

echo "========================================"
echo " SSAC NFZX IoT Platform v2.7.18"
echo " Database Connection Optimized Version"
echo " Production Environment"
echo "========================================"
echo

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "[ERROR] Java is not installed or not in PATH"
    echo "Please install Java 8+ and add it to PATH"
    exit 1
fi

echo "[INFO] Java environment check passed"
echo "[INFO] Starting SSAC NFZX IoT Platform..."
echo "[INFO] Profile: production"
echo "[INFO] Database: High-performance optimized connection pool"
echo "[INFO] Port: 9901 (HTTP), 9902 (WebSocket)"
echo "[INFO] Monitoring: http://localhost:9901/druid/ (admin/druid@2024)"
echo

# 生产环境优化的JVM参数
JVM_OPTS="-Xms1024m -Xmx4096m"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:+UseStringDeduplication"
JVM_OPTS="$JVM_OPTS -XX:+OptimizeStringConcat"
JVM_OPTS="$JVM_OPTS -XX:+UseCompressedOops"
JVM_OPTS="$JVM_OPTS -XX:+UseCompressedClassPointers"
JVM_OPTS="$JVM_OPTS -Xloggc:logs/gc-prod.log"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCDetails"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCTimeStamps"
JVM_OPTS="$JVM_OPTS -XX:+UseGCLogFileRotation"
JVM_OPTS="$JVM_OPTS -XX:NumberOfGCLogFiles=5"
JVM_OPTS="$JVM_OPTS -XX:GCLogFileSize=10M"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Duser.timezone=GMT+8"

# 生产环境数据库连接优化参数
DB_OPTS="-Ddruid.stat.logSlowSql=true"
DB_OPTS="$DB_OPTS -Ddruid.stat.slowSqlMillis=1000"
DB_OPTS="$DB_OPTS -Ddruid.stat.mergeSql=true"
DB_OPTS="$DB_OPTS -Dspring.profiles.active=prod"

# 性能监控参数
MONITOR_OPTS="-Dmanagement.endpoints.web.exposure.include=health,info,metrics,druid"
MONITOR_OPTS="$MONITOR_OPTS -Dmanagement.endpoint.health.show-details=always"

echo "[INFO] JVM Options: $JVM_OPTS"
echo "[INFO] Database Options: $DB_OPTS"
echo "[INFO] Monitor Options: $MONITOR_OPTS"
echo

# 创建日志目录
mkdir -p logs

# 启动应用
java $JVM_OPTS $DB_OPTS $MONITOR_OPTS -jar ssac-admin-nfzx-v2.7.18-optimized.jar

echo
echo "[INFO] SSAC NFZX IoT Platform stopped"
