package com.ssac.web.core.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;

/**
 * Web MVC 配置
 * 解决Spring Boot 2.7中SpringFox的兼容性问题
 * 
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        // 解决SpringFox在Spring Boot 2.7中的路径匹配问题
        configurer.setUseTrailingSlashMatch(true);
    }
}
