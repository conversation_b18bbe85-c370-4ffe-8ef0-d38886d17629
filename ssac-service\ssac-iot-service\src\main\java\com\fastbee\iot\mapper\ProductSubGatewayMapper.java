package com.ssac.iot.mapper;

import com.ssac.framework.mybatis.mapper.BaseMapperX;
import com.ssac.iot.domain.ProductSubGateway;
import com.ssac.iot.model.gateWay.ProductSubGatewayVO;

import java.util.List;

/**
 * 网关与子产品关联Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-04
 */
public interface ProductSubGatewayMapper extends BaseMapperX<ProductSubGateway>
{

    /**
     * @description: 查询网关
     * @param: productSubGateway 网关
     * @return: java.util.List<com.ssac.iot.model.gateWay.ProductSubGatewayVO>
     */
    List<ProductSubGatewayVO> selectListVO(ProductSubGateway productSubGateway);
}

