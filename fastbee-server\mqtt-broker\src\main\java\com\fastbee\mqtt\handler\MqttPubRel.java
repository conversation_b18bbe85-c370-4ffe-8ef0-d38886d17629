package com.ssac.mqtt.handler;

import com.ssac.mqtt.annotation.Process;
import com.ssac.mqtt.handler.adapter.MqttHandler;
import com.ssac.mqtt.manager.ClientManager;
import com.ssac.mqtt.manager.ResponseManager;
import com.ssac.mqtt.service.IMessageStore;
import com.ssac.base.session.Session;
import com.ssac.base.util.AttributeUtils;
import com.ssac.mqtt.utils.MqttMessageUtils;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.mqtt.MqttMessage;
import io.netty.handler.codec.mqtt.MqttMessageIdVariableHeader;
import io.netty.handler.codec.mqtt.MqttMessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 消息等级=Qos2 发布消息释放 PUBREL
 * <AUTHOR>
 */
@Slf4j
@Process(type = MqttMessageType.PUBREL)
public class MqttPubRel implements MqttHandler {

    @Autowired
    private IMessageStore messageStore;

    @Override
    public void handler(ChannelHandlerContext ctx, MqttMessage message){
        MqttMessageIdVariableHeader variableHeader = MqttMessageUtils.getIdVariableHeader(message);
        Session session = AttributeUtils.getSession(ctx.channel());
        //获取packetId
        int messageId = variableHeader.messageId();
        messageStore.removeRelInMsg(messageId);
        MqttMessage mqttMessage = MqttMessageUtils.buildPubCompMessage(message);
        ResponseManager.responseMessage(session,mqttMessage,true);
        /*更新本地ping时间*/
        ClientManager.updatePing(session.getClientId());
    }
}

