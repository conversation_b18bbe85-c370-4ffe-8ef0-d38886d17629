<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.ProductAuthorizeMapper">

    <resultMap type="com.ssac.iot.domain.ProductAuthorize" id="ProductAuthorizeResult">
        <result property="authorizeId"    column="authorize_id"    />
        <result property="authorizeCode"    column="authorize_code"    />
        <result property="productId"    column="product_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectProductAuthorizeVo">
        select authorize_id, authorize_code, product_id, device_id, serial_number, user_id, user_name,status, del_flag, create_by, create_time, update_by, update_time, remark from iot_product_authorize
    </sql>

    <select id="selectProductAuthorizeList" parameterType="com.ssac.iot.domain.ProductAuthorize" resultMap="ProductAuthorizeResult">
        <include refid="selectProductAuthorizeVo"/>
        <where>
            <if test="authorizeCode != null  and authorizeCode != ''"> and authorize_code = #{authorizeCode}</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="serialNumber != null  and serialNumber != ''"> and serial_number = #{serialNumber}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectProductAuthorizeByAuthorizeId" parameterType="Long" resultMap="ProductAuthorizeResult">
        <include refid="selectProductAuthorizeVo"/>
        where authorize_id = #{authorizeId}
    </select>

    <select id="selectFirstAuthorizeByAuthorizeCode" parameterType="com.ssac.iot.domain.ProductAuthorize" resultMap="ProductAuthorizeResult">
        <include refid="selectProductAuthorizeVo"/>
        where
        del_flag = 0
        and authorize_code = #{authorizeCode}
        and product_id = #{productId}
        limit 1
    </select>

    <select id="selectProductAuthorizeListByProductId" parameterType="com.ssac.iot.domain.ProductAuthorize" resultMap="ProductAuthorizeResult">
        <include refid="selectProductAuthorizeVo"/>
        where product_id = #{productId}
        and del_flag = 0
        order by create_time desc
    </select>

    <insert id="insertProductAuthorize" parameterType="com.ssac.iot.domain.ProductAuthorize">
        insert into iot_product_authorize
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="authorizeCode != null and authorizeCode != ''">authorize_code,</if>
            <if test="productId != null">product_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="authorizeCode != null and authorizeCode != ''">#{authorizeCode},</if>
            <if test="productId != null">#{productId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="insertBatchAuthorize" parameterType="com.ssac.iot.domain.ProductAuthorize" useGeneratedKeys="true" keyProperty="authorizeId">
        insert into iot_product_authorize (authorize_code,product_id,create_by,create_time,status)
        values
        <foreach item="item" collection="list" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.authorizeCode},#{item.productId},#{item.createBy},#{item.createTime},#{item.status}
            </trim>
        </foreach>
    </insert>

    <update id="updateProductAuthorize" parameterType="com.ssac.iot.domain.ProductAuthorize">
        update iot_product_authorize
        <trim prefix="SET" suffixOverrides=",">
            user_id = #{userId},
            device_id = #{deviceId},
            <if test="authorizeCode != null and authorizeCode != ''">authorize_code = #{authorizeCode},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where authorize_id = #{authorizeId}
    </update>

    <delete id="deleteProductAuthorizeByAuthorizeId" parameterType="Long">
        delete from iot_product_authorize where authorize_id = #{authorizeId}
    </delete>

    <delete id="deleteProductAuthorizeByAuthorizeIds" parameterType="String">
        delete from iot_product_authorize where authorize_id in
        <foreach item="authorizeId" collection="array" open="(" separator="," close=")">
            #{authorizeId}
        </foreach>
    </delete>

    <delete id="deleteProductAuthorizeByProductIds" parameterType="String">
        delete from iot_product_authorize where product_id in
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </delete>
</mapper>

