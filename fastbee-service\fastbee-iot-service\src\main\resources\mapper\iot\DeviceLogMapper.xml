<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.DeviceLogMapper">

    <resultMap type="com.ssac.iot.model.MonitorModel" id="MonitorResult">
        <result property="value"    column="log_value"    />
        <result property="time"    column="create_time"    />
    </resultMap>

    <resultMap type="com.ssac.iot.domain.DeviceLog" id="DeviceLogResult">
        <result property="logId"    column="log_id"    />
        <result property="logType"    column="log_type"    />
        <result property="logValue"    column="log_value"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="identity"    column="identity"    />
        <result property="createBy"    column="create_by"    />
        <result property="isMonitor"    column="is_monitor"    />
        <result property="mode"    column="mode"    />
        <result property="createTime"    column="create_time"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="tenantName"    column="tenant_name"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap type="com.ssac.iot.model.HistoryModel" id="HistoryResult">
        <result property="value"    column="log_value"    />
        <result property="time"    column="create_time"    />
        <result property="identity" column="identity" />
    </resultMap>

    <sql id="selectDeviceLogVo">
        select log_id, log_type, log_value, device_id, device_name,serial_number, identity, create_by, is_monitor,mode, user_id, user_name, tenant_id, tenant_name, create_time, remark from iot_device_log
    </sql>

    <select id="selectMonitorList" parameterType="com.ssac.iot.domain.DeviceLog" resultMap="MonitorResult">
        select log_value, create_time from iot_device_log
        <where>
            <if test="1==1"> and is_monitor=1</if>
            <if test="identity != null and identity != ''"> and identity = #{identity}</if>
            <if test="deviceId != null and deviceId !=0"> and device_id = #{deviceId}</if>
            <if test="serialNumber != null and serialNumber !=''"> and serial_number = #{serialNumber}</if>
            <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''"> and create_time between #{beginTime} and #{endTime}</if>
        </where>
        order by create_time desc
        limit #{total}
    </select>

    <select id="selectDeviceLogList" parameterType="com.ssac.iot.domain.DeviceLog" resultMap="DeviceLogResult">
        <include refid="selectDeviceLogVo"/>
        <where>
            <if test="isMonitor != null"> and is_monitor = #{isMonitor}</if>
            <if test="deviceId != null and deviceId !=0"> and device_id = #{deviceId}</if>
            <if test="serialNumber != null and serialNumber !=''"> and serial_number = #{serialNumber}</if>
            <if test="logType != null "> and log_type = #{logType}</if>
            <if test="logType == null "> and log_type != 7</if>
            <if test="identity != null  and identity != ''"> and identity like concat('%', #{identity}, '%')</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectDeviceLogByLogId" parameterType="Long" resultMap="DeviceLogResult">
        <include refid="selectDeviceLogVo"/>
        where log_id = #{logId}
    </select>

    <select id="selectDeviceLogCount" parameterType="Long" resultType="com.ssac.iot.model.DeviceStatistic">
        SELECT
        count(1) AS propertyCount,
        COUNT(CASE WHEN is_monitor = 1 THEN 1 END) AS monitorCount
        from iot_device_log
        <where>
            log_type = 1
            <if test="tenantId != null and tenantId != 0"> and user_id = #{userId}</if>
        </where>
    </select>

    <insert id="insertDeviceLog" parameterType="com.ssac.iot.domain.DeviceLog" useGeneratedKeys="true" keyProperty="logId">
        insert into iot_device_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="logType != null">log_type,</if>
            <if test="logValue != null">log_value,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="serialNumber != null and serialNumber != ''">serial_number,</if>
            <if test="identity != null">identity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="isMonitor != null">is_monitor,</if>
            <if test="mode != null">mode,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="tenantName != null and tenantName != ''">tenant_name,</if>
            <if test="modelName != null and modelName != ''">model_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="logType != null">#{logType},</if>
            <if test="logValue != null">#{logValue},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="serialNumber != null and serialNumber != ''">#{serialNumber},</if>
            <if test="identity != null">#{identity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="isMonitor != null">#{isMonitor},</if>
            <if test="mode != null">#{mode},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="tenantName != null and tenantName != ''">#{tenantName},</if>
            <if test="modelName != null and modelName != ''">#{modelName},</if>
         </trim>
    </insert>

    <insert id="saveBatch" parameterType="com.ssac.iot.domain.DeviceLog">
        insert into iot_device_log (log_type,log_value,device_id,device_name,serial_number,identity,create_by,
        is_monitor,mode,create_time,remark,user_id,user_name,tenant_id,tenant_name,model_name)
            values
            <foreach collection="list" item="item" index="index" separator=",">
                (#{item.logType},#{item.logValue},#{item.deviceId},#{item.deviceName},#{item.serialNumber},
                #{item.identity},#{item.createBy},#{item.isMonitor},#{item.mode},#{item.createTime},#{item.remark},
                #{item.userId},#{item.userName},#{item.tenantId},#{item.tenantName},#{item.modelName})
            </foreach>
    </insert>

    <update id="updateDeviceLog" parameterType="com.ssac.iot.domain.DeviceLog">
        update iot_device_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="logType != null">log_type = #{logType},</if>
            <if test="logValue != null">log_value = #{logValue},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="serialNumber != null and serialNumber != ''">serial_number = #{serialNumber},</if>
            <if test="identity != null">identity = #{identity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="isMonitor != null">is_monitor = #{isMonitor},</if>
            <if test="mode != null">mode = #{mode},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="tenantName != null and tenantName != ''">tenant_name = #{tenantName},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteDeviceLogByLogId" parameterType="Long">
        delete from iot_device_log where log_id = #{logId}
    </delete>

    <delete id="deleteDeviceLogByLogIds" parameterType="String">
        delete from iot_device_log where log_id in
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>

    <delete id="deleteDeviceLogByDeviceNumber" parameterType="String">
        delete from iot_device_log where serial_number = #{deviceNumber}
    </delete>

    <select id="selectHistoryList" parameterType="com.ssac.iot.domain.DeviceLog" resultMap="HistoryResult">
        select l.log_value,
               l.create_time,
               l.identity
        from iot_device_log l
        <where>
            <if test="serialNumber != null and serialNumber != ''">
                and l.serial_number = #{serialNumber}
            </if>
            <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
                and l.create_time between #{beginTime} and #{endTime}
            </if>
        </where>
        order by l.create_time desc
    </select>

    <select id="listHistory" parameterType="com.ssac.iot.domain.DeviceLog" resultMap="HistoryResult">
        select log_value,
        create_time,
        identity
        from iot_device_log
        <where>
            <if test="serialNumber != null and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
                and create_time between #{beginTime} and #{endTime}
            </if>
            <if test="identityList != null and identityList != ''">
                and identity in
                <foreach collection="identityList" item="identity" open="(" separator="," close=")">
                    #{identity}
                </foreach>
            </if>
            <if test="logType != null">
                and log_type = #{logType}
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="listhistoryGroupByCreateTime" resultType="com.ssac.iot.model.HistoryModel">
        select l.create_time as time, group_concat(distinct l.identity) as identity, group_concat(distinct l.log_value) as value
        from iot_device_log l
        <where>
            <if test="serialNumber != null and serialNumber != ''">
                and l.serial_number = #{serialNumber}
            </if>
            <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
                and l.create_time between #{beginTime} and #{endTime}
            </if>
        </where>
        group by l.create_time
        order by l.create_time desc
    </select>

    <select id="selectStatsValue" resultType="java.lang.String">
        select log_value
        from iot_device_log
        <where>
            <if test="logType != null">
                and log_type = #{logType}
            </if>
            <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
                and create_time between #{beginTime} and #{endTime}
            </if>
            <if test="identity != null and identity != ''">
                and identity = #{identity}
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
        </where>
    </select>

    <select id="countThingsModelInvoke" resultType="com.ssac.iot.model.ThingsModelLogCountVO">
        select identity identifier, min(model_name) modelName, count(identity) `count`
        from iot_device_log
        where log_type = 2
        <if test="serialNumber != null and serialNumber != ''">
            and serial_number = #{serialNumber}
        </if>
        <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
            and create_time between #{beginTime} and #{endTime}
        </if>
        group by identity
    </select>

    <select id="selectLastReport" parameterType="com.ssac.iot.domain.DeviceLog" resultMap="DeviceLogResult">
        <include refid="selectDeviceLogVo"/>
        <where>
            <if test="serialNumber != null and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="identity != null and identity != ''"> and identity = #{identity}</if>
            <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
                and create_time between #{beginTime} and #{endTime}
            </if>
        </where>
        order by create_time desc
        LIMIT 1
    </select>
</mapper>

