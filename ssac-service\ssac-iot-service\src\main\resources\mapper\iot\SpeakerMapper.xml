<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.SpeakerMapper">


    <select id="selectRelatedIdByProductIdAndUserId" resultType="java.lang.Long">
        select related_id
        from dueros_related_product
        where product_id = #{productId}
          and tenant_id = #{userId}
    </select>


    <select id="selectOauthAccessTokenByUserNameAndClientId"
            resultType="com.ssac.iot.model.speaker.OauthAccessTokenReportVO">
        select client_id, open_id
        from oauth_access_token
        where user_name = #{userName}
          and client_id = #{clientId}
    </select>

    <select id="selectOauthClientDetailsByType"
            resultType="com.ssac.iot.model.speaker.OauthClientDetailsReportVO">
        select client_id, cloud_skill_id
        from oauth_client_details
        where type = #{type}
          and status = 0
    </select>

    <select id="listAttributesByRelatedIdAndIdentifier" resultType="java.lang.String">
        select attributes
        from dueros_related_things_model
        where related_id = #{relatedId}
        and identifier in
        <foreach collection="identifierList" item="identifier" open="(" separator="," close=")">
            #{identifier}
        </foreach>
    </select>
</mapper>

