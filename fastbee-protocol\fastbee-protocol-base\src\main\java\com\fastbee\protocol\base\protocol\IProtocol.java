package com.ssac.protocol.base.protocol;


import com.ssac.common.core.mq.DeviceReport;
import com.ssac.common.core.mq.MQSendMessageBo;
import com.ssac.common.core.mq.message.DeviceData;
import com.ssac.common.core.mq.message.FunctionCallBackBo;

/**
 * 基础协议
 * <AUTHOR>
 * @date 2022/10/10 15:48
 */
public interface IProtocol {

    DeviceReport decode(DeviceData data, String clientId);

    FunctionCallBackBo encode(MQSendMessageBo message);

    /**
     * 默认方法，处理设备回复的报文编码
     */
    public default byte[] encodeCallBack(Object message) {
        return new byte[0];
    }

    default byte[] encode(DeviceData data)  {
        return new byte[0];
    }

    default DeviceData decode(String message) {
        return DeviceData.builder().build();
    }

}

