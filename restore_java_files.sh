#!/bin/bash

# Java源文件恢复脚本
# 从备份中恢复Java源文件，保持当前的目录结构

BACKUP_DIR="../springboot-backup-20250626_191509"
CURRENT_DIR="."

echo "开始从备份恢复Java源文件..."
echo "备份目录: $BACKUP_DIR"
echo "当前目录: $CURRENT_DIR"

# 检查备份目录是否存在
if [ ! -d "$BACKUP_DIR" ]; then
    echo "错误: 备份目录不存在: $BACKUP_DIR"
    exit 1
fi

# 创建目录映射函数
map_directory() {
    local old_name="$1"
    local new_name=$(echo "$old_name" | sed 's/fastbee-/ssac-/g')
    echo "$new_name"
}

# 恢复Java源文件的函数
restore_java_files() {
    local backup_module="$1"
    local current_module="$2"
    
    if [ -d "$BACKUP_DIR/$backup_module/src" ]; then
        echo "恢复 $backup_module -> $current_module"
        
        # 创建目标目录（如果不存在）
        mkdir -p "$current_module/src"
        
        # 复制Java源文件
        find "$BACKUP_DIR/$backup_module/src" -name "*.java" -type f | while read java_file; do
            # 计算相对路径
            rel_path=${java_file#$BACKUP_DIR/$backup_module/src/}
            target_file="$current_module/src/$rel_path"
            
            # 创建目标目录
            mkdir -p "$(dirname "$target_file")"
            
            # 复制文件
            cp "$java_file" "$target_file"
            echo "  复制: $rel_path"
        done
        
        # 复制资源文件
        if [ -d "$BACKUP_DIR/$backup_module/src/main/resources" ]; then
            find "$BACKUP_DIR/$backup_module/src/main/resources" -type f | while read resource_file; do
                rel_path=${resource_file#$BACKUP_DIR/$backup_module/src/main/resources/}
                target_file="$current_module/src/main/resources/$rel_path"
                
                # 创建目标目录
                mkdir -p "$(dirname "$target_file")"
                
                # 复制文件
                cp "$resource_file" "$target_file"
                echo "  复制资源: $rel_path"
            done
        fi
    fi
}

# 主要模块映射
declare -A module_mapping=(
    ["fastbee-admin"]="ssac-admin"
    ["fastbee-common"]="ssac-common"
    ["fastbee-framework"]="ssac-framework"
    ["fastbee-iot-data"]="ssac-iot-data"
    ["fastbee-mq"]="ssac-mq"
    ["fastbee-open-api"]="ssac-open-api"
    ["fastbee-record"]="ssac-record"
    ["fastbee-scada"]="ssac-scada"
)

# 恢复主要模块
for backup_module in "${!module_mapping[@]}"; do
    current_module="${module_mapping[$backup_module]}"
    restore_java_files "$backup_module" "$current_module"
done

# 恢复复杂的嵌套模块
echo "恢复嵌套模块..."

# fastbee-server -> ssac-server
if [ -d "$BACKUP_DIR/fastbee-server" ]; then
    for server_module in base-server boot-strap coap-server http-server iot-server-core mqtt-broker sip-server; do
        restore_java_files "fastbee-server/$server_module" "ssac-server/$server_module"
    done
fi

# fastbee-service -> ssac-service
if [ -d "$BACKUP_DIR/fastbee-service" ]; then
    restore_java_files "fastbee-service/fastbee-iot-service" "ssac-service/ssac-iot-service"
    restore_java_files "fastbee-service/fastbee-system-service" "ssac-service/ssac-system-service"
fi

# fastbee-protocol -> ssac-protocol
if [ -d "$BACKUP_DIR/fastbee-protocol" ]; then
    restore_java_files "fastbee-protocol/fastbee-protocol-base" "ssac-protocol/ssac-protocol-base"
    restore_java_files "fastbee-protocol/fastbee-protocol-collect" "ssac-protocol/ssac-protocol-collect"
fi

# fastbee-notify -> ssac-notify
if [ -d "$BACKUP_DIR/fastbee-notify" ]; then
    restore_java_files "fastbee-notify/fastbee-notify-core" "ssac-notify/ssac-notify-core"
    restore_java_files "fastbee-notify/fastbee-notify-web" "ssac-notify/ssac-notify-web"
fi

# fastbee-plugs -> ssac-plugs
if [ -d "$BACKUP_DIR/fastbee-plugs" ]; then
    for plugin in fastbee-generator fastbee-http fastbee-oauth fastbee-oss fastbee-quartz fastbee-ruleEngine; do
        plugin_new=$(echo "$plugin" | sed 's/fastbee-/ssac-/')
        restore_java_files "fastbee-plugs/$plugin" "ssac-plugs/$plugin_new"
    done
    
    # fastbee-pay 子模块
    if [ -d "$BACKUP_DIR/fastbee-plugs/fastbee-pay" ]; then
        restore_java_files "fastbee-plugs/fastbee-pay/fastbee-pay-api" "ssac-plugs/ssac-pay/ssac-pay-api"
        restore_java_files "fastbee-plugs/fastbee-pay/fastbee-pay-core" "ssac-plugs/ssac-pay/ssac-pay-core"
        restore_java_files "fastbee-plugs/fastbee-pay/fastbee-pay-framework" "ssac-plugs/ssac-pay/ssac-pay-framework"
    fi
fi

# 恢复独立的 mqtt-client
if [ -d "$BACKUP_DIR/fastbee-mqtt-client" ]; then
    restore_java_files "fastbee-mqtt-client" "ssac-mqtt-client"
fi

echo "Java源文件恢复完成！"
echo "请运行 'mvn clean compile' 验证编译是否成功。"
