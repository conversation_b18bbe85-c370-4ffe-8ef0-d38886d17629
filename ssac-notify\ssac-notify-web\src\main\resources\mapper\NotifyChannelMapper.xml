<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.notify.mapper.NotifyChannelMapper">

    <resultMap type="NotifyChannel" id="NotifyChannelResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="channelType"    column="channel_type"    />
        <result property="provider"    column="provider"    />
        <result property="configContent"    column="config_content"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="tenantName"    column="tenant_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectNotifyChannelVo">
        select id, name, channel_type, provider, config_content, tenant_id, tenant_name, create_by, create_time, update_by, update_time, del_flag from notify_channel
    </sql>

    <select id="selectNotifyChannelList" parameterType="NotifyChannel" resultMap="NotifyChannelResult">
        <include refid="selectNotifyChannelVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="channelType != null "> and channel_type = #{channelType}</if>
            <if test="provider != null  and provider != ''"> and provider = #{provider}</if>
            <if test="configContent != null  and configContent != ''"> and config_content = #{configContent}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectNotifyChannelById" parameterType="Long" resultMap="NotifyChannelResult">
        <include refid="selectNotifyChannelVo"/>
        where id = #{id}
    </select>

    <select id="selectNotifyChannelByIds" resultType="com.ssac.notify.domain.NotifyChannel">
        <include refid="selectNotifyChannelVo"/>
        where id in
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertNotifyChannel" parameterType="NotifyChannel" useGeneratedKeys="true" keyProperty="id">
        insert into notify_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="channelType != null">channel_type,</if>
            <if test="provider != null and provider != ''">provider,</if>
            <if test="configContent != null and configContent != ''">config_content,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="tenantName != null and tenantName != ''">tenant_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="channelType != null">#{channelType},</if>
            <if test="provider != null and provider != ''">#{provider},</if>
            <if test="configContent != null and configContent != ''">#{configContent},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="tenantName != null and tenantName != ''">#{tenantName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateNotifyChannel" parameterType="NotifyChannel">
        update notify_channel
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="channelType != null">channel_type = #{channelType},</if>
            <if test="provider != null and provider != ''">provider = #{provider},</if>
            <if test="configContent != null and configContent != ''">config_content = #{configContent},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="tenantName != null and tenantName != ''">tenant_name = #{tenantName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNotifyChannelById" parameterType="Long">
        delete from notify_channel where id = #{id}
    </delete>

    <delete id="deleteNotifyChannelByIds" parameterType="String">
        delete from notify_channel where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

