SSAC NFZX IoT Platform - Version Information
===========================================

Version: v2.7.18
Build Date: 2025-01-26 23:39:52
Build Environment: Spring Boot 2.7.18
Package Size: 248MB

Features:
- Complete NFZX Protocol Support
- NFZX Environment Configuration
- Redis Cluster Support
- MQTT Integration
- Device Management
- Alarm Processing
- Rule Engine (LiteFlow)
- Multi-Environment Support

Protocols Supported:
- NFZX-JSON: South Star JSON Protocol
- NFZX-JSON-CONTROL: South Star Control Protocol
- Standard IoT Protocols

Database Support:
- MySQL: nfzxiot database
- Redis: Distributed caching
- TDengine: Time-series data (optional)

Network Ports:
- HTTP: 9901
- WebSocket: 9902
- MQTT: 1883

JVM Requirements:
- Minimum: Java 8
- Recommended: Java 11+
- Memory: 1GB+ (Recommended: 4GB+)

Git Information:
- Repository: SSAC-smart-v2.5.1-A
- Branch: main
- Tags: v2.7.18-nfzx-migration, v2.7.18-nfzx-config-migration

Build Status: SUCCESS
Test Status: SKIPPED
Package Status: READY FOR PRODUCTION

Checksum (SHA256):
[Generated during build process]

Contact: SSAC Development Team
License: Proprietary
