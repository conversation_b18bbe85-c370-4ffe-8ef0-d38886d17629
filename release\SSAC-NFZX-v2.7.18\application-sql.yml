# 数据源配置
spring:
  datasource:
    dynamic:
      druid:
        initial-size: 5
        min-idle: 10
        max-wait: 60000
        max-active: 20
        timeBetweenEvictionRunsMillis: 60000  # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        minEvictableIdleTimeMillis: 300000    # 配置一个连接在池中最小生存的时间，单位是毫秒
        maxEvictableIdleTimeMillis: 900000    # 配置一个连接在池中最大生存的时间，单位是毫秒
        validation-query: 'SELECT 1'
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
      datasource:
        master:
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ************************************************************************************************************************************************
          username: root
          password: fastbee
          druid:
            filters: stat,wall
            stat:
              # 慢SQL记录
              log-slow-sql: true
              slow-sql-millis: 1000
              merge-sql: true
            wall:
              none-base-statement-allow: true
        taos: # 配置 taos 数据源
          enabled: true
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.taosdata.jdbc.TSDBDriver
          url: *****************************************************************
          username: root
          password: taosdata
          dbName: fastbee_log
#        SQLServer: # 配置 SQLServer 数据源
#          type: com.alibaba.druid.pool.DruidDataSource
#          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#          url: ****************************************************
#          username: sa
#          password: wumei@Tencent
#       postgres: # 配置 postgres 数据源
#         type: com.alibaba.druid.pool.DruidDataSource
#         driver-class-name: org.postgresql.Driver
#         url: ********************************************
#         username: postgres
#         password: fastbee
#       dameng: # 配置达梦数据源
#         type: com.alibaba.druid.pool.DruidDataSource
#         driver-class-name: dm.jdbc.driver.DmDriver
#         url: jdbc:dm://************:5236?schema=FASTBEE&useSSL=false&useUnicode=true&characterEncoding=UTF-8&serverTimezone=UTC
#         username: FASTBEE
#         password: wumei-Tencent
#       oracle: # 配置 oracle 数据源
#         type: com.alibaba.druid.pool.DruidDataSource
#         driver-class-name: oracle.jdbc.driver.OracleDriver
#         url: ********************************************
#         username: FASTBEE
#         password: fastbee
#         druid:
#           validation-query: 'SELECT 1 FROM DUAL'
#        slave:
#          type: com.alibaba.druid.pool.DruidDataSource
#          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: *************************************************************************************************************************************************
#          username: root
#          password: fastbee

  shardingsphere:
    props:
      # 是否显示 ShardingSpher 的sql，用于Debug
      sql-show: true
    datasource:
      # 配置真实数据源
      names: ds0
      ds0: # 配置 mysql 数据源
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ************************************************************************************************************************************************
        username: root
        password: fastbee
        filters: stat,wall
        filter:
          stat:
            enabled: true
            # 慢SQL记录
            log-slow-sql: true
            slow-sql-millis: 1000
            merge-sql: true
          wall:
            config:
              multi-statement-allow: true

    rules: # 配置表规则
      sharding:
        # 表策略配置
        tables:
          # iot_device_log 是逻辑表
          iot_device_log:
            actualDataNodes: ds0.iot_device_log_$->{2024..2030}0$->{1..9},ds0.iot_device_log_$->{2024..2030}1$->{0..2}
            tableStrategy:
              # 使用标准分片策略
              standard:
                # 配置分片字段
                shardingColumn: create_time
                # 分片算法名称，不支持大写字母和下划线，否则启动就会报错
                shardingAlgorithmName: time-sharding-algorithm
        # 分片算法配置
        shardingAlgorithms:
          # 分片算法名称，不支持大写字母和下划线，否则启动就会报错
          time-sharding-algorithm:
            # 类型：自定义策略
            type: CLASS_BASED
            props:
              # 分片策略
              strategy: standard
              # 分片算法类
              algorithmClassName: com.fastbee.framework.config.sharding.TimeShardingAlgorithm

  # redis 配置
  redis:
    host: localhost                         # 地址
    port: 6379                              # 端口，默认为6379
    database: 3                             # 数据库索引
    password: fastbee                       # 密码
    timeout: 10s                            # 连接超时时间
    lettuce:
      pool:
        min-idle: 0                         # 连接池中的最小空闲连接
        max-idle: 8                         # 连接池中的最大空闲连接
        max-active: 8                       # 连接池的最大数据库连接数
        max-wait: -1ms                      # 连接池最大阻塞等待时间（使用负值表示没有限制）
  # mqtt 配置
  mqtt:
    username: fastbee                       # 账号
    password: fastbee                       # 密码
    host-url: tcp://localhost:1883          # mqtt连接tcp地址
    client-id: ${random.int}                # 客户端Id，不能相同，采用随机数 ${random.value}
    default-topic: test                     # 默认主题
    timeout: 30                             # 超时时间
    keepalive: 30                           # 保持连接
    clearSession: true                      # 清除会话(设置为false,断开连接，重连后使用原来的会话 保留订阅的主题，能接收离线期间的消息)

# sip 配置
sip:
  enabled: true                            # 是否启用视频监控SIP，true为启用
  ## 默认为容器IP，IDE启动可以写本地网卡内网IP或者127.0.0.1
  ## 本地调试需保持设备与服务器在同一局域网
  ip: **************
  port: 5061                                # SIP端口(保持默认)
  domain: 3402000000                        # 由省级、市级、区级、基层编号组成
  id: 34020000002000000001                  # 同上，另外增加编号，(可保持默认)
  password: 12345678                        # 监控设备接入的密码
  log: true
  zlmRecordPath: /opt/media/bin/www

# 日志配置
logging:
  level:
    com.fastbee: debug
    com.yomahub: debug
    org.dromara: warn
    org.springframework: warn
    com.baomidou: debug

# Swagger配置
swagger:
  enabled: true                             # 是否开启swagger
  pathMapping: /dev-api                     # 请求前缀

