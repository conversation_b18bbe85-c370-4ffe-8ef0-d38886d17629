package com.ssac.system.service;


import com.ssac.system.domain.AppLanguage;

import java.util.List;

/**
 * app语言Service接口
 */
public interface IAppLanguageService
{
    /**
     * 查询app语言
     *
     * @param id app语言主键
     * @return app语言
     */
    public AppLanguage selectAppLanguageById(Long id);

    /**
     * 查询app语言列表
     *
     * @param appLanguage app语言
     * @return app语言集合
     */
    public List<AppLanguage> selectAppLanguageList(AppLanguage appLanguage);

    /**
     * 新增app语言
     *
     * @param appLanguage app语言
     * @return 结果
     */
    public int insertAppLanguage(AppLanguage appLanguage);

    /**
     * 修改app语言
     *
     * @param appLanguage app语言
     * @return 结果
     */
    public int updateAppLanguage(AppLanguage appLanguage);

    /**
     * 批量删除app语言
     *
     * @param ids 需要删除的app语言主键集合
     * @return 结果
     */
    public int deleteAppLanguageByIds(Long[] ids);

    /**
     * 删除app语言信息
     *
     * @param id app语言主键
     * @return 结果
     */
    public int deleteAppLanguageById(Long id);
}

