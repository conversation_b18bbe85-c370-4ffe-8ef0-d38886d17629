<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.SocialUserMapper">

    <resultMap type="com.ssac.iot.domain.SocialUser" id="SocialUserResult">
        <result property="socialUserId" column="social_user_id"/>
        <result property="uuid" column="uuid"/>
        <result property="source" column="source"/>
        <result property="accessToken" column="access_token"/>
        <result property="expireIn" column="expire_in"/>
        <result property="refreshToken" column="refresh_token"/>
        <result property="openId" column="open_id"/>
        <result property="uid" column="uid"/>
        <result property="accessCode" column="access_code"/>
        <result property="unionId" column="union_id"/>
        <result property="scope" column="scope"/>
        <result property="tokenType" column="token_type"/>
        <result property="idToken" column="id_token"/>
        <result property="macAlgorithm" column="mac_algorithm"/>
        <result property="macKey" column="mac_key"/>
        <result property="code" column="code"/>
        <result property="oauthToken" column="oauth_token"/>
        <result property="oauthTokenSecret" column="oauth_token_secret"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="sysUserId" column="sys_user_id"/>
        <result property="username" column="username"/>
        <result property="nickname" column="nickname"/>
        <result property="avatar" column="avatar"/>
        <result property="gender" column="gender"/>
        <result property="sourceClient" column="source_client"/>
    </resultMap>

    <sql id="selectSocialUserVo">
        select social_user_id,
               uuid,
               source,
               access_token,
               expire_in,
               refresh_token,
               open_id,
               uid,
               access_code,
               union_id,
               scope,
               token_type,
               id_token,
               mac_algorithm,
               mac_key,
               code,
               oauth_token,
               oauth_token_secret,
               create_time,
               create_by,
               update_time,
               update_by,
               del_flag,
               status,
               sys_user_id,
               username,
               nickname,
               avatar,
               gender,
               source_client
        from iot_social_user
    </sql>

    <select id="selectSocialUserList" parameterType="com.ssac.iot.domain.SocialUser" resultMap="SocialUserResult">
        <include refid="selectSocialUserVo"/>
        <where>
            <if test="uuid != null  and uuid != ''">and uuid = #{uuid}</if>
            <if test="source != null  and source != ''">and source = #{source}</if>
            <if test="accessToken != null  and accessToken != ''">and access_token = #{accessToken}</if>
            <if test="expireIn != null ">and expire_in = #{expireIn}</if>
            <if test="refreshToken != null  and refreshToken != ''">and refresh_token = #{refreshToken}</if>
            <if test="openId != null  and openId != ''">and open_id = #{openId}</if>
            <if test="uid != null  and uid != ''">and uid = #{uid}</if>
            <if test="accessCode != null  and accessCode != ''">and access_code = #{accessCode}</if>
            <if test="unionId != null  and unionId != ''">and union_id = #{unionId}</if>
            <if test="scope != null  and scope != ''">and scope = #{scope}</if>
            <if test="tokenType != null  and tokenType != ''">and token_type = #{tokenType}</if>
            <if test="idToken != null  and idToken != ''">and id_token = #{idToken}</if>
            <if test="macAlgorithm != null  and macAlgorithm != ''">and mac_algorithm = #{macAlgorithm}</if>
            <if test="macKey != null  and macKey != ''">and mac_key = #{macKey}</if>
            <if test="code != null  and code != ''">and code = #{code}</if>
            <if test="oauthToken != null  and oauthToken != ''">and oauth_token = #{oauthToken}</if>
            <if test="oauthTokenSecret != null  and oauthTokenSecret != ''">and oauth_token_secret =
                #{oauthTokenSecret}
            </if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="sysUserId != null ">and sys_user_id = #{sysUserId}</if>
            <if test="username != null  and username != ''">and username like concat('%', #{username}, '%')</if>
            <if test="nickname != null  and nickname != ''">and nickname like concat('%', #{nickname}, '%')</if>
            <if test="avatar != null  and avatar != ''">and avatar = #{avatar}</if>
            <if test="gender != null ">and gender = #{gender}</if>
            and del_flag = 0
        </where>
    </select>

    <select id="selectSocialUserBySocialUserId" parameterType="Long" resultMap="SocialUserResult">
        <include refid="selectSocialUserVo"/>
        where social_user_id = #{socialUserId}
        and del_flag = 0
    </select>

    <select id="selectOneByOpenIdAndUnionId" resultMap="SocialUserResult">
        <include refid="selectSocialUserVo"/>
        where open_id = #{openId}
        and del_flag = 0
        <if test="unionId != null and unionId != ''">and union_id = #{unionId}</if>
    </select>

    <select id="selectSysUserIdByUnionId" resultType="java.lang.Long">
        select sys_user_id
        from iot_social_user
        where union_id = #{unionId}
        and sys_user_id is not null
        and del_flag = 0
        order by social_user_id desc
        limit 1
    </select>

    <select id="selectBySysUserId" resultType="com.ssac.iot.domain.SocialUser">
        <include refid="selectSocialUserVo"/>
        where sys_user_id = #{sysUserId}
        and del_flag = 0
    </select>

    <select id="selectByUserIdAndSourceClient" resultType="com.ssac.iot.domain.SocialUser">
        <include refid="selectSocialUserVo"/>
        where sys_user_id = #{sysUserId}
        and source_client = #{sourceClient}
        and del_flag = 0
    </select>

    <select id="listWechatPublicAccountOpenId" resultType="com.ssac.iot.domain.SocialUser">
        select DISTINCT u.sys_user_id, s.open_id
        from iot_social_user u left join iot_social_user s on u.union_id = s.union_id
        where u.sys_user_id in
            <foreach collection="userIdList" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
          and u.status = 1
          and u.del_flag = 0
          and s.del_flag = 0
          and s.`status` =1
          and s.source_client = 'wechat_open_public_account'
    </select>


    <insert id="insertSocialUser" parameterType="com.ssac.iot.domain.SocialUser" useGeneratedKeys="true" keyProperty="socialUserId">
        insert into iot_social_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uuid != null and uuid != ''">uuid,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="accessToken != null and accessToken != ''">access_token,</if>
            <if test="expireIn != null">expire_in,</if>
            <if test="refreshToken != null">refresh_token,</if>
            <if test="openId != null">open_id,</if>
            <if test="uid != null">uid,</if>
            <if test="accessCode != null">access_code,</if>
            <if test="unionId != null">union_id,</if>
            <if test="scope != null">scope,</if>
            <if test="tokenType != null">token_type,</if>
            <if test="idToken != null">id_token,</if>
            <if test="macAlgorithm != null">mac_algorithm,</if>
            <if test="macKey != null">mac_key,</if>
            <if test="code != null">code,</if>
            <if test="oauthToken != null">oauth_token,</if>
            <if test="oauthTokenSecret != null">oauth_token_secret,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="sysUserId != null">sys_user_id,</if>
            <if test="username != null">username,</if>
            <if test="nickname != null">nickname,</if>
            <if test="avatar != null">avatar,</if>
            <if test="gender != null">gender,</if>
            <if test="sourceClient != null and sourceClient != ''">source_client,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uuid != null and uuid != ''">#{uuid},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="accessToken != null and accessToken != ''">#{accessToken},</if>
            <if test="expireIn != null">#{expireIn},</if>
            <if test="refreshToken != null">#{refreshToken},</if>
            <if test="openId != null">#{openId},</if>
            <if test="uid != null">#{uid},</if>
            <if test="accessCode != null">#{accessCode},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="scope != null">#{scope},</if>
            <if test="tokenType != null">#{tokenType},</if>
            <if test="idToken != null">#{idToken},</if>
            <if test="macAlgorithm != null">#{macAlgorithm},</if>
            <if test="macKey != null">#{macKey},</if>
            <if test="code != null">#{code},</if>
            <if test="oauthToken != null">#{oauthToken},</if>
            <if test="oauthTokenSecret != null">#{oauthTokenSecret},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="sysUserId != null">#{sysUserId},</if>
            <if test="username != null">#{username},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="gender != null">#{gender},</if>
            <if test="sourceClient != null and sourceClient != ''">#{sourceClient},</if>
        </trim>
    </insert>

    <update id="updateSocialUser" parameterType="com.ssac.iot.domain.SocialUser">
        update iot_social_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="uuid != null and uuid != ''">uuid = #{uuid},</if>
            <if test="source != null and source != ''">source = #{source},</if>
            <if test="accessToken != null and accessToken != ''">access_token = #{accessToken},</if>
            <if test="expireIn != null">expire_in = #{expireIn},</if>
            <if test="refreshToken != null">refresh_token = #{refreshToken},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="uid != null">uid = #{uid},</if>
            <if test="accessCode != null">access_code = #{accessCode},</if>
            <if test="unionId != null">union_id = #{unionId},</if>
            <if test="scope != null">scope = #{scope},</if>
            <if test="tokenType != null">token_type = #{tokenType},</if>
            <if test="idToken != null">id_token = #{idToken},</if>
            <if test="macAlgorithm != null">mac_algorithm = #{macAlgorithm},</if>
            <if test="macKey != null">mac_key = #{macKey},</if>
            <if test="code != null">code = #{code},</if>
            <if test="oauthToken != null">oauth_token = #{oauthToken},</if>
            <if test="oauthTokenSecret != null">oauth_token_secret = #{oauthTokenSecret},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="sysUserId != null">sys_user_id = #{sysUserId},</if>
            <if test="username != null">username = #{username},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="sourceClient != null and sourceClient != ''">source_client = #{sourceClient},</if>
        </trim>
        where social_user_id = #{socialUserId}
    </update>

    <delete id="deleteSocialUserBySocialUserId" parameterType="Long">
        delete
        from iot_social_user
        where social_user_id = #{socialUserId}
    </delete>

    <delete id="deleteSocialUserBySocialUserIds" parameterType="String">
        delete from iot_social_user where social_user_id in
        <foreach item="socialUserId" collection="array" open="(" separator="," close=")">
            #{socialUserId}
        </foreach>
    </delete>

    <delete id="deleteBySysUserIdAndSourceClient">
        update iot_social_user
        set del_flag = 1,
            update_time = now()
        where sys_user_id = #{sysUserId}
        and source_client in
        <foreach item="sourceClient" collection="sourceClientList" open="(" separator="," close=")">
            #{sourceClient}
        </foreach>
    </delete>

    <delete id="deleteBySysUserIdsAndSourceClient">
        update iot_social_user
        set del_flag = 1,
        update_time = now()
        where sys_user_id in
        <foreach item="sysUserId" collection="sysUserIds" open="(" separator="," close=")">
            #{sysUserId}
        </foreach>
        and source_client in
        <foreach item="sourceClient" collection="sourceClientList" open="(" separator="," close=")">
            #{sourceClient}
        </foreach>
    </delete>

    <delete id="deleteByOpenIdAndSourceClient">
        update iot_social_user
        set del_flag = 1,
            update_time = now()
        where open_id = #{openId}
        and source_client = #{sourceClient}
    </delete>
</mapper>

