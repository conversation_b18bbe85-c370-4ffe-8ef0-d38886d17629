package ${packageName}.service;

import java.util.List;
import ${packageName}.domain.${ClassName};
import ${packageName}.model.${ClassName}VO;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service extends IService<${ClassName}>
{

    /**
     * 查询${functionName}列表
     *
     * @param ${className} ${functionName}
     * @return ${functionName}集合
     */
     List<${ClassName}VO> select${ClassName}VOList(${ClassName} ${className});

    /**
     * 查询${functionName}
     *
     * @param ${pkColumn.javaField} 主键
     * @return ${functionName}
     */
     ${ClassName} select${ClassName}ById(${pkColumn.javaType} ${pkColumn.javaField});

    /**
     * 查询${functionName}
     *
     * @param ${pkColumn.javaField} 主键
     * @return ${functionName}
     */
    ${ClassName} queryByIdWithCache(${pkColumn.javaType} ${pkColumn.javaField});

    /**
     * 新增${functionName}
     *
     * @param ${className} ${functionName}
     * @return 是否新增成功
     */
    Boolean insertWithCache(${ClassName} ${className});

    /**
     * 修改${functionName}
     *
     * @param ${className} ${functionName}
     * @return 是否修改成功
     */
    Boolean updateWithCache(${ClassName} ${className});

    /**
     * 校验并批量删除${functionName}信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithCacheByIds(${pkColumn.javaType}[] ids, Boolean isValid);

}
