<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.DeviceShareMapper">

    <resultMap type="com.ssac.iot.domain.DeviceShare" id="DeviceShareResult">
        <result property="deviceId"    column="device_id"    />
        <result property="userId"    column="user_id"    />
        <result property="phonenumber" column="phonenumber"    />
        <result property="perms"    column="perms"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="userName" column="user_name"/>
    </resultMap>

    <resultMap type="com.ssac.common.core.domain.entity.SysUser" id="ShareUserResult">
        <id     property="userId"       column="user_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="email"        column="email"        />
        <result property="phonenumber"  column="phonenumber"  />
        <result property="sex"          column="sex"          />
        <result property="avatar"       column="avatar"       />
        <result property="createTime"   column="create_time"  />
        <result property="remark"       column="remark"       />
    </resultMap>

    <sql id="selectDeviceShareVo">
        select device_id, user_id, phonenumber, perms, del_flag, create_by, create_time, update_by, update_time, remark from iot_device_share
    </sql>

    <select id="selectDeviceShareList" parameterType="com.ssac.iot.domain.DeviceShare" resultMap="DeviceShareResult">

        select d.device_id, d.user_id, d.phonenumber, d.perms, d.del_flag,
               d.create_by, d.create_time, d.update_by, d.update_time, d.remark,
               su.user_name
        from iot_device_share d left join sys_user su on d.user_id = su.user_id
        <where>
            <if test="1==1"> and d.device_id = #{deviceId}</if>
            <if test="userId != null and userId != 0"> and d.user_id = #{userId}</if>
            <if test="phonenumber != null  and phonenumber != ''"> and d.phonenumber = #{phonenumber}</if>
        </where>
    </select>

    <select id="selectDeviceShareByDeviceId" parameterType="Long" resultMap="DeviceShareResult">
        <include refid="selectDeviceShareVo"/>
        where device_id = #{deviceId}
    </select>

    <insert id="insertDeviceShare" parameterType="com.ssac.iot.domain.DeviceShare">
        insert into iot_device_share
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="phonenumber != null">phonenumber,</if>
            <if test="perms != null">perms,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="phonenumber != null">#{phonenumber},</if>
            <if test="perms != null">#{perms},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDeviceShare" parameterType="com.ssac.iot.domain.DeviceShare">
        update iot_device_share
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="phonenumber != null">phonenumber = #{phonenumber},</if>
            <if test="perms != null">perms = #{perms},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where device_id = #{deviceId}
    </update>

    <delete id="deleteDeviceShareByDeviceId" parameterType="Long">
        delete from iot_device_share where device_id = #{deviceId}
    </delete>

    <delete id="deleteDeviceShareByDeviceIds" parameterType="String">
        delete from iot_device_share where device_id in
        <foreach item="deviceId" collection="array" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </delete>

    <delete id="deleteDeviceShareByDeviceIdAndUserId">
        delete from iot_device_share
        where device_id = #{deviceId}
        and user_id = #{userId}
    </delete>

    <select id="selectDeviceShareByDeviceIdAndUserId" resultMap="DeviceShareResult">
        <include refid="selectDeviceShareVo"/>
        where device_id = #{deviceId} and user_id = #{userId}
    </select>

    <select id="selectShareUser" parameterType="com.ssac.iot.domain.DeviceShare" resultMap="ShareUserResult">
        SELECT
            u.user_id,
            u.nick_name,
            u.user_name,
            u.email,
            u.avatar,
            u.phonenumber,
            u.sex,
            u.create_time
        FROM
            sys_user u
                LEFT JOIN (
                SELECT
                    *
                FROM
                    iot_device_share
                WHERE
                    iot_device_share.device_id = #{deviceId}) d on u.user_id = d.user_id

        WHERE
            u.del_flag = '0'
          AND u.STATUS = 0
          AND u.phonenumber = #{phonenumber} and d.device_id is null
          and isnull(u.dept_id)
    </select>
</mapper>

