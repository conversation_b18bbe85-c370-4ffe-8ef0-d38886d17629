<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.SocialPlatformMapper">

    <resultMap type="com.ssac.iot.domain.SocialPlatform" id="SocialPlatformResult">
        <result property="socialPlatformId" column="social_platform_id"/>
        <result property="platform" column="platform"/>
        <result property="status" column="status"/>
        <result property="clientId" column="client_id"/>
        <result property="secretKey" column="secret_key"/>
        <result property="redirectUri" column="redirect_uri"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="remark" column="remark"/>
        <result property="bindUri" column="bind_uri"/>
        <result property="redirectLoginUri" column="redirect_login_uri"/>
        <result property="errorMsgUri" column="error_msg_uri"/>
    </resultMap>

    <sql id="selectSocialPlatformVo">
        select social_platform_id,
               platform,
               status,
               client_id,
               secret_key,
               redirect_uri,
               del_flag,
               create_by,
               create_time,
               update_time,
               update_by,
               remark,
               bind_uri,
               redirect_login_uri,
               error_msg_uri
        from iot_social_platform
    </sql>

    <select id="selectSocialPlatformList" parameterType="com.ssac.iot.domain.SocialPlatform" resultMap="SocialPlatformResult">
        <include refid="selectSocialPlatformVo"/>
        <where>
            <if test="platform != null  and platform != ''">and platform = #{platform}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="clientId != null  and clientId != ''">and client_id = #{clientId}</if>
            <if test="secretKey != null  and secretKey != ''">and secret_key = #{secretKey}</if>
            <if test="redirectUri != null  and redirectUri != ''">and redirect_uri = #{redirectUri}</if>
            <if test="bindUri != null  and bindUri != ''">and bind_uri = #{bindUri}</if>
            <if test="redirectLoginUri != null  and redirectLoginUri != ''">and redirect_login_uri =
                #{redirectLoginUri}
            </if>
            <if test="errorMsgUri != null ">and error_msg_uri = #{errorMsgUri}</if>
        </where>
    </select>

    <select id="selectSocialPlatformBySocialPlatformId" parameterType="Long" resultMap="SocialPlatformResult">
        <include refid="selectSocialPlatformVo"/>
        where social_platform_id = #{socialPlatformId}
    </select>

    <select id="selectSocialPlatformByPlatform" parameterType="String" resultMap="SocialPlatformResult">
        <include refid="selectSocialPlatformVo"/>
        where platform = #{platform}
    </select>

    <insert id="insertSocialPlatform" parameterType="com.ssac.iot.domain.SocialPlatform" useGeneratedKeys="true"
            keyProperty="socialPlatformId">
        insert into iot_social_platform
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="platform != null and platform != ''">platform,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="clientId != null and clientId != ''">client_id,</if>
            <if test="secretKey != null and secretKey != ''">secret_key,</if>
            <if test="redirectUri != null and redirectUri != ''">redirect_uri,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="bindUri != null and bindUri != ''">bind_uri,</if>
            <if test="redirectLoginUri != null and redirectLoginUri != ''">redirect_login_uri,</if>
            <if test="errorMsgUri != null and errorMsgUri != ''">error_msg_uri,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="platform != null and platform != ''">#{platform},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="clientId != null and clientId != ''">#{clientId},</if>
            <if test="secretKey != null and secretKey != ''">#{secretKey},</if>
            <if test="redirectUri != null and redirectUri != ''">#{redirectUri},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="bindUri != null and bindUri != ''">#{bindUri},</if>
            <if test="redirectLoginUri != null and redirectLoginUri != ''">#{redirectLoginUri},</if>
            <if test="errorMsgUri != null and errorMsgUri != ''">#{errorMsgUri},</if>
        </trim>
    </insert>

    <update id="updateSocialPlatform" parameterType="com.ssac.iot.domain.SocialPlatform">
        update iot_social_platform
        <trim prefix="SET" suffixOverrides=",">
            <if test="platform != null and platform != ''">platform = #{platform},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="clientId != null and clientId != ''">client_id = #{clientId},</if>
            <if test="secretKey != null and secretKey != ''">secret_key = #{secretKey},</if>
            <if test="redirectUri != null and redirectUri != ''">redirect_uri = #{redirectUri},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="bindUri != null and bindUri != ''">bind_uri = #{bindUri},</if>
            <if test="redirectLoginUri != null and redirectLoginUri != ''">redirect_login_uri = #{redirectLoginUri},
            </if>
            <if test="errorMsgUri != null and errorMsgUri != ''">error_msg_uri = #{errorMsgUri},</if>
        </trim>
        where social_platform_id = #{socialPlatformId}
    </update>

    <delete id="deleteSocialPlatformBySocialPlatformId" parameterType="Long">
        delete
        from iot_social_platform
        where social_platform_id = #{socialPlatformId}
    </delete>

    <delete id="deleteSocialPlatformBySocialPlatformIds" parameterType="String">
        delete from iot_social_platform where social_platform_id in
        <foreach item="socialPlatformId" collection="array" open="(" separator="," close=")">
            #{socialPlatformId}
        </foreach>
    </delete>
</mapper>
