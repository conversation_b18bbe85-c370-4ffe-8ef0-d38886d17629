<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ssac</artifactId>
        <groupId>com.ssac</groupId>
        <version>3.8.5</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>ssac-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>

        <!-- spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 表示依赖不会传�?-->
        </dependency>

        <!-- swagger3-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <!-- 防止进入swagger页面报类型转换错误，排除3.0.0中的引用，手动增�?.6.2版本 -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.6.2</version>
        </dependency>

        <!-- Mysql驱动�?-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-framework</artifactId>
        </dependency>

        <!-- 定时任务-->
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-quartz</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-generator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-oss</artifactId>
        </dependency>

        <!-- controller API模块-->
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-open-api</artifactId>
        </dependency>
        <!--服务集成启动模块-->
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>boot-strap</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>http-server</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>sip-server</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-http</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>liteflow-core</artifactId>
            <version>${liteflow.version}</version>
        </dependency>

        <!-- 通知配置模块 -->
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-notify-web</artifactId>
        </dependency>
        <!-- 通知api模块 -->
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-notify-core</artifactId>
        </dependency>

        <!-- oauth2.0 -->
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-oauth</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

<!--        &lt;!&ndash; 小度音箱 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.ssac</groupId>-->
<!--            <artifactId>ssac-link-dueros</artifactId>-->
<!--        </dependency>-->

        <!-- 组态模�?-->
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-scada</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-common</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.1.RELEASE</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>

