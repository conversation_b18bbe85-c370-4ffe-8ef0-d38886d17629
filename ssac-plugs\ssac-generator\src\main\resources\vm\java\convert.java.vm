package ${packageName}.convert;

import ${packageName}.domain.${ClassName};
import ${packageName}.model.${ClassName}VO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * ${functionName}Convert转换类
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Mapper
public interface ${ClassName}Convert
{

    ${ClassName}Convert INSTANCE = Mappers.getMapper(${ClassName}Convert.class);

    /**
     * 实体类转换为VO类
     *
     * @param ${className}
     * @return ${functionName}集合
     */
    ${ClassName}VO convert${ClassName}VO(${ClassName} ${className});

    /**
     * VO类转换为实体类集合
     *
     * @param ${className}VO
     * @return ${functionName}集合
     */
    ${ClassName} convert${ClassName}(${ClassName}VO ${className}VO);

    /**
     * 实体类转换为VO类集合
     *
     * @param ${className}List
     * @return ${functionName}集合
     */
    List<${ClassName}VO> convert${ClassName}VOList(List<${ClassName}> ${className}List);

    /**
     * VO类转换为实体类
     *
     * @param ${className}VOList
     * @return ${functionName}集合
     */
    List<${ClassName}> convert${ClassName}List(List<${ClassName}VO> ${className}VOList);
}
