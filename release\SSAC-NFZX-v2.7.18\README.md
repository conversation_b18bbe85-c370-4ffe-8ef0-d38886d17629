# 🚀 SSAC NFZX IoT Platform v2.7.18 - 正式发布版

## 📋 发布概览

**版本**: v2.7.18  
**发布日期**: 2025-01-26  
**基础框架**: Spring Boot 2.7.18  
**包含功能**: 完整的NFZX协议支持 + SSAC智能控制平台

## 📦 发布包内容

```
SSAC-NFZX-v2.7.18/
├── ssac-admin-nfzx-v2.7.18.jar     # 主应用JAR包 (248MB)
├── application.yml                  # 主配置文件
├── application-dev.yml              # 开发环境配置
├── application-prod.yml             # 生产环境配置
├── application-sql.yml              # SQL配置文件
├── start-dev.bat                    # Windows开发环境启动脚本
├── start-prod.bat                   # Windows生产环境启动脚本
├── start-dev.sh                     # Linux开发环境启动脚本
├── start-prod.sh                    # Linux生产环境启动脚本
├── NFZX_PROTOCOL_MIGRATION_GUIDE.md # NFZX协议迁移指南
├── NFZX_CONFIG_MIGRATION_GUIDE.md   # NFZX配置迁移指南
└── README.md                        # 本文档
```

## 🎯 新增功能特性

### ✅ NFZX协议支持
- **NFZX-JSON**: 南方之星JSON协议
- **NFZX-JSON-CONTROL**: 南方之星控制协议
- **电力参数监控**: 电压、电流、功率、电能等
- **告警处理**: 32位告警映射和MQTT告警发送
- **设备控制**: 配置下发和参数设置

### ✅ 环境配置
- **NFZX数据库**: nfzxiot数据库支持
- **NFZX Redis**: 专用Redis服务器配置
- **NFZX MQTT**: 专用MQTT服务器配置
- **集群支持**: Redis集群和负载均衡

## 🔧 系统要求

### 最低要求
- **Java**: JDK 8 或更高版本
- **内存**: 最少 1GB RAM
- **磁盘**: 最少 2GB 可用空间
- **网络**: 需要访问数据库、Redis、MQTT服务器

### 推荐配置
- **Java**: JDK 11 或更高版本
- **内存**: 4GB RAM 或更多
- **磁盘**: 10GB 可用空间
- **CPU**: 4核心或更多

## 🚀 快速启动

### Windows环境

#### 开发环境启动
```batch
# 双击运行或命令行执行
start-dev.bat
```

#### 生产环境启动
```batch
# 双击运行或命令行执行
start-prod.bat
```

### Linux环境

#### 开发环境启动
```bash
# 给脚本执行权限
chmod +x start-dev.sh

# 启动应用
./start-dev.sh
```

#### 生产环境启动
```bash
# 给脚本执行权限
chmod +x start-prod.sh

# 启动应用
./start-prod.sh
```

### 手动启动

#### 开发环境
```bash
java -Xms512m -Xmx2048m -jar ssac-admin-nfzx-v2.7.18.jar --spring.profiles.active=dev
```

#### 生产环境
```bash
java -Xms1024m -Xmx4096m -jar ssac-admin-nfzx-v2.7.18.jar --spring.profiles.active=prod
```

## 🔧 配置说明

### 开发环境配置 (application-dev.yml)
- **数据库**: *************:3306/nfzxiot
- **Redis**: *************:6379 (密码: cceste660)
- **MQTT**: tcp://*************:1883 (admin/NFZX@2024)
- **服务端口**: 9901

### 生产环境配置 (application-prod.yml)
- **数据库**: 127.0.0.1:3306/nfzxiot
- **Redis**: 127.0.0.1:6379 (密码: cceste660)
- **MQTT**: tcp://*************:1883 (admin/NFZX@2024)
- **服务端口**: 9901

## 🌐 访问地址

启动成功后，可通过以下地址访问：

- **主页面**: http://localhost:9901
- **API文档**: http://localhost:9901/dev-api/swagger-ui.html
- **WebSocket**: ws://localhost:9902/mqtt

## 📊 JVM参数说明

### 开发环境参数
```
-Xms512m -Xmx2048m -XX:+UseG1GC -XX:+UseStringDeduplication
```

### 生产环境参数
```
-Xms1024m -Xmx4096m -XX:+UseG1GC -XX:+UseStringDeduplication 
-XX:MaxGCPauseMillis=200 -XX:+DisableExplicitGC
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```
Error: Port 9901 is already in use
```
**解决方案**: 检查端口占用或修改配置文件中的端口号

#### 2. 数据库连接失败
```
Error: Could not connect to database
```
**解决方案**: 检查数据库服务是否启动，网络是否通畅，用户名密码是否正确

#### 3. Redis连接失败
```
Error: Could not connect to Redis
```
**解决方案**: 检查Redis服务是否启动，密码是否正确

#### 4. 内存不足
```
Error: OutOfMemoryError
```
**解决方案**: 增加JVM内存参数 -Xmx

### 日志查看
应用启动后会在控制台输出日志，如需保存日志到文件：

```bash
# 重定向日志到文件
java -jar ssac-admin-nfzx-v2.7.18.jar --spring.profiles.active=prod > app.log 2>&1 &
```

## 📚 相关文档

- **NFZX协议迁移指南**: `NFZX_PROTOCOL_MIGRATION_GUIDE.md`
- **NFZX配置迁移指南**: `NFZX_CONFIG_MIGRATION_GUIDE.md`

## 🆕 版本更新记录

### v2.7.18 (2025-01-26)
- ✅ 完整迁移NFZX协议支持
- ✅ 应用NFZX环境配置
- ✅ 优化性能和资源管理
- ✅ 增强错误处理和日志记录
- ✅ 支持集群部署

## 📞 技术支持

如遇到问题，请检查：
1. Java版本是否符合要求
2. 网络连接是否正常
3. 配置文件是否正确
4. 服务依赖是否启动

---
**SSAC NFZX IoT Platform v2.7.18**  
**构建时间**: 2025-01-26 23:39  
**包大小**: 248MB  
**状态**: ✅ 生产就绪
