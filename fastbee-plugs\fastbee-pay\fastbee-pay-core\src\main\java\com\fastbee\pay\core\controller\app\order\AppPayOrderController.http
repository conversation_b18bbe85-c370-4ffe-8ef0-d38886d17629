### /pay/create 提交支付订单【alipay_pc】
POST {{appApi}}/pay/order/submit
Content-Type: application/json
Authorization: Bearer {{appToken}}
tenant-id: {{appTenentId}}

{
  "id": 174,
  "channelCode": "alipay_pc"
}

### /pay/create 提交支付订单【wx_bar】
POST {{appApi}}/pay/order/submit
Content-Type: application/json
Authorization: Bearer {{appToken}}
tenant-id: {{appTenentId}}

{
  "id": 202,
  "channelCode": "wx_bar",
  "channelExtras": {
    "authCode": "134042110834344848"
  }
}

### /pay/create 提交支付订单【wx_pub】
POST {{appApi}}/pay/order/submit
Content-Type: application/json
Authorization: Bearer {{appToken}}
tenant-id: {{appTenentId}}

{
  "id": 202,
  "channelCode": "wx_pub",
  "channelExtras": {
    "openid": "ockUAwIZ-0OeMZl9ogcZ4ILrGba0"
  }
}

### /pay/create 提交支付订单【wx_lite】
POST {{appApi}}/pay/order/submit
Content-Type: application/json
Authorization: Bearer {{appToken}}
tenant-id: {{appTenentId}}

{
  "id": 202,
  "channelCode": "wx_lite",
  "channelExtras": {
    "openid": "oLefc4g5GjKWHJjLjMSXB3wX0fD0"
  }
}

### /pay/create 提交支付订单【wx_native】
POST {{appApi}}/pay/order/submit
Content-Type: application/json
Authorization: Bearer {{appToken}}
tenant-id: {{appTenentId}}

{
  "id": 202,
  "channelCode": "wx_native"
}
