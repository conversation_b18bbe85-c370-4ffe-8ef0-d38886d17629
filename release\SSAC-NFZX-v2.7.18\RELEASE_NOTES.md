# 🎉 SSAC NFZX IoT Platform v2.7.18 - 正式发布

## 📅 发布信息

**发布版本**: v2.7.18  
**发布日期**: 2025-01-26  
**构建时间**: 23:39:52  
**包大小**: 248MB  
**状态**: ✅ 生产就绪

## 🚀 重大更新

### ✅ NFZX协议完整支持
- **新增协议**: NFZX-JSON 和 NFZX-JSON-CONTROL
- **电力监控**: 支持电压、电流、功率、电能等参数监控
- **告警处理**: 32位告警映射和实时MQTT告警发送
- **设备控制**: 完整的设备配置和参数下发功能
- **数据转换**: 自动应用工程单位转换系数

### ✅ NFZX环境配置
- **数据库**: 完整的nfzxiot数据库配置
- **Redis**: 专用Redis服务器和集群支持
- **MQTT**: 专用MQTT服务器配置和认证
- **端口**: HTTP 9901, WebSocket 9902
- **多环境**: 开发和生产环境独立配置

### ✅ 技术架构升级
- **Spring Boot**: 升级到2.7.18版本
- **集群支持**: Redis集群和负载均衡
- **规则引擎**: LiteFlow 2.12.4集成
- **性能优化**: JVM参数和连接池优化

## 📦 发布包内容

```
SSAC-NFZX-v2.7.18/
├── ssac-admin-nfzx-v2.7.18.jar     # 主应用JAR包 (248MB)
├── application*.yml                 # 配置文件
├── start-*.bat/sh                   # 启动脚本
├── README.md                        # 使用说明
├── VERSION.txt                      # 版本信息
├── CHECKSUM.sha256                  # 校验和
├── NFZX_PROTOCOL_MIGRATION_GUIDE.md # 协议迁移指南
├── NFZX_CONFIG_MIGRATION_GUIDE.md   # 配置迁移指南
└── RELEASE_NOTES.md                 # 本文档
```

## 🔧 系统要求

### 运行环境
- **Java**: JDK 8+ (推荐 JDK 11+)
- **内存**: 最少 1GB (推荐 4GB+)
- **磁盘**: 最少 2GB 可用空间
- **操作系统**: Windows/Linux/macOS

### 外部依赖
- **MySQL**: 5.7+ 或 8.0+
- **Redis**: 5.0+ (支持集群)
- **MQTT Broker**: EMQ X 或兼容的MQTT服务器

## 🚀 快速部署

### 1. 环境准备
```bash
# 确保Java环境
java -version

# 确保数据库服务
mysql -h 127.0.0.1 -P 3306 -u root -p

# 确保Redis服务
redis-cli ping
```

### 2. 启动应用

#### Windows环境
```batch
# 开发环境
start-dev.bat

# 生产环境
start-prod.bat
```

#### Linux环境
```bash
# 开发环境
./start-dev.sh

# 生产环境
./start-prod.sh
```

### 3. 验证部署
- **主页面**: http://localhost:9901
- **API文档**: http://localhost:9901/dev-api/swagger-ui.html
- **健康检查**: http://localhost:9901/actuator/health

## 🔍 验证测试

### ✅ 构建验证
- **编译**: 成功，无错误
- **打包**: 成功，生成248MB JAR包
- **启动**: 成功，显示SSAC启动横幅
- **协议**: NFZX协议自动注册成功

### ✅ 功能验证
- **Spring Boot**: 2.7.18版本正确加载
- **LiteFlow**: 规则引擎正常初始化
- **配置**: 多环境配置正确切换
- **端口**: 9901端口正常监听

## 📊 性能参数

### JVM配置
```bash
# 开发环境
-Xms512m -Xmx2048m -XX:+UseG1GC

# 生产环境
-Xms1024m -Xmx4096m -XX:+UseG1GC -XX:MaxGCPauseMillis=200
```

### 连接池配置
- **数据库连接池**: 32个连接
- **Redis连接池**: 32个连接
- **MQTT连接**: 固定客户端ID
- **线程池**: G1GC优化

## 🔒 安全特性

- **认证**: 数据库和Redis密码保护
- **网络**: MQTT TLS支持
- **配置**: 敏感信息环境变量化
- **日志**: 生产环境日志级别优化

## 📈 监控指标

- **应用监控**: Spring Boot Actuator
- **JVM监控**: G1GC性能指标
- **数据库监控**: Druid连接池监控
- **业务监控**: 设备连接和协议解析统计

## 🐛 已知问题

1. **数据库连接**: 需要确保MySQL服务可访问
2. **Redis连接**: 需要正确的Redis密码配置
3. **MQTT连接**: 需要网络访问MQTT服务器
4. **端口冲突**: 确保9901和9902端口未被占用

## 🔄 升级说明

### 从旧版本升级
1. 备份现有配置文件
2. 停止旧版本服务
3. 替换JAR包
4. 更新配置文件
5. 启动新版本

### 数据库迁移
- 无需数据库结构变更
- 兼容现有数据
- 新增NFZX协议配置表

## 📞 技术支持

### 常见问题
- 查看 `README.md` 获取详细使用说明
- 查看 `NFZX_PROTOCOL_MIGRATION_GUIDE.md` 了解协议详情
- 查看 `NFZX_CONFIG_MIGRATION_GUIDE.md` 了解配置详情

### 故障排除
1. 检查Java版本和环境变量
2. 验证数据库和Redis连接
3. 确认网络和防火墙设置
4. 查看应用日志获取详细错误信息

## 🎯 下一步计划

- 性能优化和压力测试
- 更多设备协议支持
- 集群部署和高可用
- 监控和告警系统完善

---

**SSAC NFZX IoT Platform v2.7.18**  
**© 2025 SSAC Technology**  
**构建状态**: ✅ SUCCESS  
**测试状态**: ✅ VERIFIED  
**发布状态**: ✅ READY FOR PRODUCTION
