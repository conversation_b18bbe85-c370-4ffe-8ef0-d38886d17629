package com.ssac.iot.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ssac.common.annotation.Excel;
import com.ssac.common.core.domain.BaseEntity;

import java.util.HashMap;
import java.util.Map;

/**
 * 指令偏好设置对象 command_preferences
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@ApiModel(value = "CommandPreferences", description = "指令偏好设置 command_preferences")
@Data
@TableName("command_preferences")
public class CommandPreferences {

    /**
     * 指令id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 指令名称
     */
    @Excel(name = "指令名称")
    @ApiModelProperty("指令名称")
    private String name;

    /**
     * 指令
     */
    @Excel(name = "指令")
    @ApiModelProperty("指令")
    private String command;

    /**
     * 设备编号
     */
    @Excel(name = "设备编号")
    @ApiModelProperty("设备编号")
    private String serialNumber;

    @Setter
    @TableField(exist = false)
    @ApiModelProperty("请求参数")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Map<String, Object> params;

    public Map<String, Object> getParams(){
        if (params == null){
            params = new HashMap<>();
        }
        return params;
    }

}

