<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.GroupMapper">
    
    <resultMap type="com.ssac.iot.domain.Group" id="GroupResult">
        <result property="groupId"    column="group_id"    />
        <result property="groupName"    column="group_name"    />
        <result property="groupOrder"    column="group_order"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap type="com.ssac.iot.model.IdOutput" id="IdsResult">
        <result property="id"    column="device_id"    />
    </resultMap>

    <sql id="selectGroupVo">
        select group_id, group_name, group_order, user_id, user_name,  create_time, update_time, remark from iot_group
    </sql>

    <select id="selectGroupList" parameterType="com.ssac.iot.domain.Group" resultMap="GroupResult">
        <include refid="selectGroupVo"/>
        <where>  
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
        order by group_order
    </select>
    
    <select id="selectGroupByGroupId" parameterType="Long" resultMap="GroupResult">
        <include refid="selectGroupVo"/>
        where group_id = #{groupId}
    </select>

    <select id="selectDeviceIdsByGroupId" parameterType="Long" resultMap="IdsResult">
            select device_id from iot_device_group where group_id=#{groupId}
    </select>
        
    <insert id="insertGroup" parameterType="com.ssac.iot.domain.Group" useGeneratedKeys="true" keyProperty="groupId">
        insert into iot_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupName != null and groupName != ''">group_name,</if>
            <if test="groupOrder != null">group_order,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupName != null and groupName != ''">#{groupName},</if>
            <if test="groupOrder != null">#{groupOrder},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="insertDeviceGroups" parameterType="com.ssac.iot.model.DeviceGroupInput">
        insert into iot_device_group (device_id,group_id)
        values
        <foreach item="deviceId" collection="deviceIds" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{deviceId},#{groupId},
            </trim>
        </foreach>
    </insert>

    <update id="updateGroup" parameterType="com.ssac.iot.domain.Group">
        update iot_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupName != null and groupName != ''">group_name = #{groupName},</if>
            <if test="groupOrder != null">group_order = #{groupOrder},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where group_id = #{groupId}
    </update>

    <delete id="deleteGroupByGroupId" parameterType="Long">
        delete from iot_group where group_id = #{groupId}
    </delete>

    <delete id="deleteGroupByGroupIds" parameterType="String">
        delete from iot_group where group_id in 
        <foreach item="groupId" collection="array" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </delete>

    <delete id="deleteDeviceGroupByGroupIds" parameterType="String">
        delete from iot_device_group where group_id in
        <foreach item="groupId" collection="array" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </delete>

    <select id="selectGroupListByUserId" parameterType="java.lang.Long" resultMap="GroupResult">
        <include refid="selectGroupVo"/>
        <where>
            user_id = #{userId}
        </where>
        order by group_order
    </select>

    <select id="listAllByIdList" parameterType="java.util.Set" resultMap="GroupResult">
        <include refid="selectGroupVo"/>
        <where>
            group_id in
            <foreach collection="collection" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </where>
        order by group_order
    </select>
</mapper>
