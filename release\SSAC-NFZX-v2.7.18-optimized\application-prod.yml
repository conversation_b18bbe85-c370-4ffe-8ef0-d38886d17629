# 数据源配置
spring:
  datasource:
    dynamic:
      druid:
        initial-size: 15                      # 生产环境初始连接数更多
        min-idle: 15                          # 最小空闲连接数
        max-wait: 8000                        # 获取连接等待超时：8秒
        max-active: 100                       # 生产环境最大活跃连接数更多
        timeBetweenEvictionRunsMillis: 30000  # 检测间隔：30秒
        minEvictableIdleTimeMillis: 180000    # 最小空闲时间：3分钟
        maxEvictableIdleTimeMillis: 300000    # 最大空闲时间：5分钟
        validation-query: 'SELECT 1'
        validationQueryTimeout: 3             # 验证查询超时：3秒
        testWhileIdle: true
        testOnBorrow: true                    # 开启借用时验证
        testOnReturn: false
        removeAbandoned: true                 # 移除废弃连接
        removeAbandonedTimeout: 300           # 废弃连接超时：5分钟
        logAbandoned: true                    # 记录废弃连接日志
        keepAlive: true                       # 保持连接活跃
        keepAliveBetweenTimeMillis: 60000     # 保活检测间隔：1分钟
        # 生产环境监控配置
        web-stat-filter:
          enabled: true
          url-pattern: /*
          exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
        stat-view-servlet:
          enabled: true
          url-pattern: /druid/*
          reset-enable: false
          login-username: admin
          login-password: druid@2024
      datasource:
        master:
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************************************************************************************************************************
          username: root
          password: NFZX505050
          druid:
            filters: stat,wall
            stat:
              # 慢SQL记录
              log-slow-sql: true
              slow-sql-millis: 1000
              merge-sql: true
            wall:
              none-base-statement-allow: true
        taos: # 配置 taos 数据源
          enabled: false      # 默认不启用TDengine，true=启用，false=不启用
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.taosdata.jdbc.TSDBDriver
          url: *****************************************************************
          username: root
          password: taosdata
          dbName: fastbee_log
#        slave:
#          type: com.alibaba.druid.pool.DruidDataSource
#          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: *************************************************************************************************************************************************
#          username: root
#          password: fastbee
  shardingsphere:
#    props:
      # 是否显示 ShardingSpher 的sql，用于Debug
#      sql-show: true
    datasource:
      # 配置真实数据源
      names: ds0
      ds0: # 配置 mysql 数据源
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: **************************************************************************************************************************************************************************************************************************************************
        username: root
        password: NFZX505050
        filters: stat,wall
        filter:
          stat:
            enabled: true
            # 慢SQL记录
            log-slow-sql: true
            slow-sql-millis: 1000
            merge-sql: true
          wall:
            config:
              multi-statement-allow: true

    rules: # 配置表规则
      sharding:
        # 表策略配置
        tables:
          # iot_device_log 是逻辑表
          iot_device_log:
            actualDataNodes: ds0.iot_device_log_$->{2024..2030}0$->{1..9},ds0.iot_device_log_$->{2024..2030}1$->{0..2}
            tableStrategy:
              # 使用标准分片策略
              standard:
                # 配置分片字段
                shardingColumn: create_time
                # 分片算法名称，不支持大写字母和下划线，否则启动就会报错
                shardingAlgorithmName: time-sharding-algorithm
        # 分片算法配置
        shardingAlgorithms:
          # 分片算法名称，不支持大写字母和下划线，否则启动就会报错
          time-sharding-algorithm:
            # 类型：自定义策略
            type: CLASS_BASED
            props:
              # 分片策略
              strategy: standard
              # 分片算法类
              algorithmClassName: com.ssac.framework.config.sharding.TimeShardingAlgorithm
  # redis 配置
  redis:
    host: 127.0.0.1                        # 地址
    port: 6379                              # 端口，默认为6379
    password: 'cceste660'                   # 密码
    database: 3                             # 数据库索引
    timeout: 10s                            # 连接超时时间
    lettuce:
      pool:
        min-idle: 0                         # 连接池中的最小空闲连接
        max-idle: 8                         # 连接池中的最大空闲连接
        max-active: 8                       # 连接池的最大数据库连接数
        max-wait: -1ms                      # 连接池最大阻塞等待时间（使用负值表示没有限制）
  # mqtt 配置
  mqtt:
    username: admin                        # 账号
    password: NFZX@2024                       # 密码
    host-url: tcp://*************:1883             # mqtt连接tcp地址
    client-id: 1111111                # 客户端Id，不能相同，采用随机数 ${random.value}
    default-topic: test                     # 默认主题
    timeout: 30                             # 超时时间
    keepalive: 30                           # 保持连接
    clearSession: true                      # 清除会话(设置为false,断开连接，重连后使用原来的会话 保留订阅的主题，能接收离线期间的消息)

# redisson 配置
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${ssac.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

# sip 配置
sip:
  enabled: true                             # 是否启用视频监控SIP，true为启用
  ## 本地调试时，绑定网卡局域网IP，设备在同一局域网，设备接入IP填写绑定IP
  ## 部署服务端时，默认绑定容器IP，设备接入IP填写服务器公网IP
  ip: **********
  port: 5061                                # SIP端口(保持默认)
  domain: 3402000000                        # 由省级、市级、区级、基层编号组成
  id: 34020000002000000001                  # 同上，另外增加编号，(可保持默认)
  password: 12345678                        # 监控设备接入的密码
  log: false
  zlmRecordPath: /opt/media/bin/www
  mp4MaxSecond: 30                        # 视频录像时长，单位秒

# 日志配置
logging:
  level:
    com.fastbee: info
    com.yomahub: debug
    org.dromara: warn
    org.springframework: warn

# Swagger配置
swagger:
  enabled: true                             # 是否开启swagger
  pathMapping: /dev-api                     # 请求前缀

liteflow:
  #FlowExecutor的execute2Future的线程数，默认为64
  main-executor-works: 64
  #FlowExecutor的execute2Future的自定义线程池Builder
  main-executor-class: com.fastbee.ruleEngine.config.MainExecutorBuilder
  #并行节点的线程池Builder
  thread-executor-class: com.fastbee.ruleEngine.config.WhenExecutorBuilder
  rule-source-ext-data-map:
    # 应用名称，规则链和脚本组件名称需要一致，不要修改
    applicationName: fastbee
    #是否开启SQL日志
    sqlLogEnabled: true
    # 规则多时，启用快速加载模式
    fast-load: false
    #是否开启SQL数据轮询自动刷新机制 默认不开启
    pollingEnabled: false
    pollingIntervalSeconds: 60
    pollingStartSeconds: 60
    #以下是chain表的配置
    chainTableName: iot_scene
    chainApplicationNameField: application_name
    chainNameField: chain_name
    elDataField: el_data
    chainEnableField: enable
    #以下是script表的配置
    scriptTableName: iot_script
    scriptApplicationNameField: application_name
    scriptIdField: script_id
    scriptNameField: script_name
    scriptDataField: script_data
    scriptTypeField: script_type
    scriptLanguageField: script_language
    scriptEnableField: enable
