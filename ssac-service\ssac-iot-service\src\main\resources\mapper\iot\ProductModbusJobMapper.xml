<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.ProductModbusJobMapper">

    <resultMap type="ProductModbusJob" id="ProductModbusJobResult">
        <result property="taskId"    column="task_id"    />
        <result property="jobName"    column="job_name"    />
        <result property="productId"    column="product_id"    />
        <result property="command"    column="command"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectProductModbusJobVo">
        select task_id, job_name, product_id, command, status, create_by, create_time, remark from iot_product_modbus_job
    </sql>

</mapper>
