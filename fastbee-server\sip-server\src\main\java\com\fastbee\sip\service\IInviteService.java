package com.ssac.sip.service;

import com.ssac.sip.enums.SessionType;
import com.ssac.sip.model.InviteInfo;
import com.ssac.sip.model.VideoSessionInfo;

import java.util.List;

public interface IInviteService {

    void updateInviteInfo(VideoSessionInfo sinfo, InviteInfo inviteInfo);

    InviteInfo getInviteInfo(SessionType type,
                             String deviceId,
                             String channelId,
                             String stream);

    List<InviteInfo> getInviteInfoAll(SessionType type, String deviceId, String channelId, String stream);

    InviteInfo getInviteInfoBySSRC(String ssrc);

    void removeInviteInfo(SessionType type,
                          String deviceId,
                          String channelId,
                          String stream);
}

