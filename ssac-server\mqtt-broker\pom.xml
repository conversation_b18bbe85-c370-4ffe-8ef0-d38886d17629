<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>ssac-server</artifactId>
		<groupId>com.ssac</groupId>
		<version>3.8.5</version>
	</parent>

	<artifactId>mqtt-broker</artifactId>
	<description>基于netty搭建的mqttBroker</description>

	<dependencies>
		<dependency>
			<groupId>com.ssac</groupId>
			<artifactId>iot-server-core</artifactId>
		</dependency>

		<dependency>
			<groupId>com.ssac</groupId>
			<artifactId>ssac-mq</artifactId>
		</dependency>
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>sip-server</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ssac</groupId>
            <artifactId>ssac-mqtt-client</artifactId>
        </dependency>
    </dependencies>


</project>

