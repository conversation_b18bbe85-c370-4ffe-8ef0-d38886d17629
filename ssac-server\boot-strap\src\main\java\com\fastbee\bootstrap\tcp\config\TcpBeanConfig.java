package com.ssac.bootstrap.tcp.config;

import com.ssac.protocol.WModelManager;
import com.ssac.base.core.HandlerMapping;
import com.ssac.base.core.SpringHandlerMapping;
import com.ssac.base.session.SessionListener;
import com.ssac.base.session.SessionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class TcpBeanConfig {

    @Bean
    public HandlerMapping handlerMapping(){
        return new SpringHandlerMapping();
    }

    @Bean
    public TcpHandlerInterceptor handlerInterceptor(){
        return new TcpHandlerInterceptor();
    }

    @Bean
    public SessionListener sessionListener(){
        return new TcpSessionListener();
    }

    @Bean
    public SessionManager sessionManager(SessionListener sessionListener){
        return new SessionManager(sessionListener);
    }

    @Bean
    public WModelManager wModelManager(){
        return new WModelManager("com.ssac.modbus");
    }


}

