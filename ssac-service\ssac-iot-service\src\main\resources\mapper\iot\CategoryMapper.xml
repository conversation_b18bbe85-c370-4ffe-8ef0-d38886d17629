<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.CategoryMapper">

    <resultMap type="com.ssac.iot.domain.Category" id="CategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="tenantName"    column="tenant_name"    />
        <result property="isSys"    column="is_sys"    />
        <result property="orderNum"    column="order_num"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap type="com.ssac.iot.model.IdAndName" id="CategoryShortResult">
        <result property="id"    column="category_id"    />
        <result property="name"    column="category_name"    />
    </resultMap>

    <sql id="selectCategoryVo">
        select category_id, category_name, tenant_id, tenant_name, is_sys,order_num, create_time, update_time, remark from iot_category
    </sql>

    <select id="selectCategoryList" parameterType="com.ssac.iot.domain.Category" resultMap="CategoryResult">
        select c.category_id, c.category_name, c.tenant_id, c.tenant_name,
               c.is_sys,c.order_num, c.create_time, c.update_time, c.remark
        from iot_category c
        <where>
            <if test="deptId != null and showSenior and !isAdmin">
                and ( c.tenant_id = #{tenantId}
                or (c.tenant_id in (
                SELECT de.dept_user_id
                FROM sys_dept de
                WHERE FIND_IN_SET( de.dept_id,(
                SELECT d.ancestors
                FROM sys_dept d
                WHERE d.dept_id = #{deptId} )
                )
                )
                )
                )
            </if>
            <if test="!showSenior and tenantId != null  and tenantId != 0 and !isAdmin">
                and c.tenant_id = #{tenantId}
            </if>
            <if test="categoryName != null  and categoryName != ''"> and c.category_name like concat('%', #{categoryName}, '%')</if>
            <if test="tenantId != null  and tenantId != ''"> and (c.tenant_id = #{tenantId} or c.is_sys = 1)</if>
        </where>
        order by order_num
    </select>

    <select id="selectCategoryShortList"  resultMap="CategoryShortResult">
        select c.category_id, c.category_name
        from iot_category c
        <where>
            <if test="deptId != null and showSenior and !isAdmin">
                and ( c.tenant_id = #{tenantId}
                or (c.tenant_id in (
                SELECT de.dept_user_id
                FROM sys_dept de
                WHERE FIND_IN_SET( de.dept_id,(
                SELECT d.ancestors
                FROM sys_dept d
                WHERE d.dept_id = #{deptId} )
                )
                )
                )
                )
            </if>
            <if test="!showSenior and tenantId != null  and tenantId != 0 and !isAdmin">
                and c.tenant_id = #{tenantId}
            </if>
            <if test="categoryName != null  and categoryName != ''"> and c.category_name like concat('%', #{categoryName}, '%')</if>
            <if test="tenantId != null  and tenantId != ''"> and (c.tenant_id = #{tenantId} or c.is_sys = 1)</if>
        </where>
        order by order_num
    </select>

    <select id="selectCategoryByCategoryId" parameterType="Long" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        where category_id = #{categoryId}
    </select>

    <insert id="insertCategory" parameterType="com.ssac.iot.domain.Category" useGeneratedKeys="true" keyProperty="categoryId">
        insert into iot_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="tenantName != null and tenantName != ''">tenant_name,</if>
            <if test="isSys != null">is_sys,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="tenantName != null and tenantName != ''">#{tenantName},</if>
            <if test="isSys != null">#{isSys},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCategory" parameterType="com.ssac.iot.domain.Category">
        update iot_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="tenantName != null and tenantName != ''">tenant_name = #{tenantName},</if>
            <if test="isSys != null">is_sys = #{isSys},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteCategoryByCategoryId" parameterType="Long">
        delete from iot_category where category_id = #{categoryId}
    </delete>

    <delete id="deleteCategoryByCategoryIds" parameterType="String">
        delete from iot_category where category_id in
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

    <select id="productCountInCategorys" parameterType="String" resultType="int">
        select count(1) from iot_product where category_id in
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </select>
</mapper>

