package ${packageName}.vo;

#foreach ($import in $importList)
import ${import};
#end
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fastbee.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */

@ApiModel(value = "${ClassName}VO", description = "${functionName} ${tableName}")
@Data
public class ${ClassName}VO{

#foreach ($column in $columns)
    /** $column.columnComment */
    #if($foreach.index == 0)
    #end
    #set($parentheseIndex=$column.columnComment.indexOf("（"))
    #if($parentheseIndex != -1)
        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
    #else
        #set($comment=$column.columnComment)
    #end
    #if($parentheseIndex != -1)
    @ApiModelProperty("${comment}")
    @Excel(name = "${comment}")
    #elseif($column.javaType == 'Date')
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("${comment}")
    @Excel(name = "${comment}")
    #else
    @Excel(name = "${comment}")
    @ApiModelProperty("${comment}")
    #end
    private $column.javaType $column.javaField;

#end

#if($table.sub)
/** $table.subTable.functionName信息 */
private List<${subClassName}> ${subclassName}List;
#end
}
