<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.ProductSubGatewayMapper">

    <resultMap type="ProductSubGateway" id="ProductSubGatewayResult">
        <result property="id"    column="id"    />
        <result property="gwProductId"    column="gw_product_id"    />
        <result property="subProductId"    column="sub_product_id"    />
        <result property="slaveId"    column="slave_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectProductSubGatewayVo">
        select id, gw_product_id, sub_product_id, slave_id, create_by, create_time, update_by, update_time, remark from iot_product_sub_gateway
    </sql>

    <select id="selectListVO" resultType="com.ssac.iot.model.gateWay.ProductSubGatewayVO">
        select ps.id, ps.gw_product_id, ps.sub_product_id, ps.slave_id, ps.create_by, ps.create_time, ps.update_by, ps.update_time, ps.remark, ps.create_time,
               p.product_name subProductName, p.location_way subLocationWay
        from iot_product_sub_gateway ps left join iot_product p on ps.sub_product_id = p.product_id
        where ps.gw_product_id = #{gwProductId}
        order by create_time
    </select>

</mapper>

