@echo off
title SSAC NFZX IoT Platform v2.7.18 - Development Environment (Database Optimized)

echo.
echo ========================================
echo  SSAC NFZX IoT Platform v2.7.18
echo  Database Connection Optimized Version
echo  Development Environment
echo ========================================
echo.

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java is not installed or not in PATH
    echo Please install Java 8+ and add it to PATH
    pause
    exit /b 1
)

echo [INFO] Java environment check passed
echo [INFO] Starting SSAC NFZX IoT Platform...
echo [INFO] Profile: development
echo [INFO] Database: Optimized connection pool configuration
echo [INFO] Port: 9901 (HTTP), 9902 (WebSocket)
echo [INFO] Monitoring: http://localhost:9901/druid/ (admin/admin)
echo.

REM 优化的JVM参数 - 开发环境
set JVM_OPTS=-Xms512m -Xmx2048m
set JVM_OPTS=%JVM_OPTS% -XX:+UseG1GC
set JVM_OPTS=%JVM_OPTS% -XX:MaxGCPauseMillis=200
set JVM_OPTS=%JVM_OPTS% -XX:+PrintGCDetails
set JVM_OPTS=%JVM_OPTS% -XX:+PrintGCTimeStamps
set JVM_OPTS=%JVM_OPTS% -Xloggc:logs/gc-dev.log
set JVM_OPTS=%JVM_OPTS% -Dfile.encoding=UTF-8
set JVM_OPTS=%JVM_OPTS% -Duser.timezone=GMT+8

REM 数据库连接优化参数
set DB_OPTS=-Ddruid.stat.logSlowSql=true
set DB_OPTS=%DB_OPTS% -Ddruid.stat.slowSqlMillis=2000
set DB_OPTS=%DB_OPTS% -Dspring.profiles.active=dev

echo [INFO] JVM Options: %JVM_OPTS%
echo [INFO] Database Options: %DB_OPTS%
echo.

REM 启动应用
java %JVM_OPTS% %DB_OPTS% -jar ssac-admin-nfzx-v2.7.18-optimized.jar

echo.
echo [INFO] SSAC NFZX IoT Platform stopped
pause
