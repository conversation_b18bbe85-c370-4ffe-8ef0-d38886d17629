<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.iot.mapper.ThingsModelMapper">
    <resultMap type="com.ssac.iot.domain.ThingsModel" id="ThingsModelResult">
        <result property="modelId" column="model_id"/>
        <result property="modelName" column="model_name"/>
        <result property="modelName_zh_CN" column="model_name_zh_cn"/>
        <result property="modelName_en_US" column="model_name_en_us"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="tenantName" column="tenant_name"/>
        <result property="identifier" column="identifier"/>
        <result property="type" column="type"/>
        <result property="datatype" column="datatype"/>
        <result property="formula" column="formula"/>
        <result property="specs" column="specs"/>
        <result property="isChart" column="is_chart"/>
        <result property="isSharePerm" column="is_share_perm"/>
        <result property="isHistory" column="is_history"/>
        <result property="isMonitor" column="is_monitor"/>
        <result property="isApp" column="is_app"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="isReadonly" column="is_readonly"/>
        <result property="modelOrder" column="model_order"/>
    </resultMap>

    <resultMap type="com.ssac.iot.model.ThingsModelPerm" id="ThingsModelPermResult">
        <result property="modelName" column="model_name"/>
        <result property="identifier" column="identifier"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <resultMap type="com.ssac.iot.model.modbus.ModbusAndThingsVO" id="ThingsModelAndModbusResult">
        <result property="modelId" column="model_id"/>
        <result property="modelName" column="model_name"/>
        <result property="identifier" column="identifier"/>
        <result property="isSelect" column="is_select"/>
        <result property="dataType" column="datatype"/>
    </resultMap>

    <sql id="selectThingsModelVo">
        select model_id,
               model_name,
               product_id,
               product_name,
               tenant_id,
               tenant_name,
               identifier,
               type,
               datatype,
               formula,
               specs,
               is_chart,
               is_share_perm,
               is_history,
               is_monitor,
               is_app,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_readonly,
               model_order
        from iot_things_model
    </sql>

    <select id="selectThingsModelList" parameterType="com.ssac.iot.domain.ThingsModel" resultMap="ThingsModelResult">
        select m.model_id,
        case
        when #{language} = 'zh-CN' then m.model_name
        when #{language} = 'en-US' then t.en_us
        else m.model_name
        end as model_name,
        m.model_name as model_name_zh_cn, t.en_us as model_name_en_us,
        m.product_id, m.product_name, m.tenant_id, m.tenant_name, m.identifier, m.type, m.datatype, m.formula,
        m.specs, m.is_chart, m.is_share_perm, m.is_history, m.is_monitor, m.is_app, m.del_flag, m.create_by,
        m.create_time, m.update_by, m.update_time, m.remark, m.is_readonly, m.model_order
        from iot_things_model m
        left join iot_things_model_translate t on m.model_id = t.id
        <where>
            <if test="productId != null">
                and m.product_id = #{productId}
            </if>
            <if test="type!=null">
                and type = #{type}
            </if>
            <if test="isChart != null ">
                and is_chart = #{isChart}
            </if>
            <if test="isHistory != null ">
                and is_history = #{isHistory}
            </if>
            <if test="isMonitor != null ">
                and is_monitor = #{isMonitor}
            </if>
            <if test="isReadonly != null ">
                and is_readonly = #{isReadonly}
            </if>
            <if test="modelIdList != null ">
                model_id in
                <foreach collection="modelIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by model_order desc,create_time desc
    </select>

    <select id="selectThingsModelPermList" resultMap="ThingsModelPermResult">
        select case
                   when #{language} = 'zh-CN' then m.model_name
                   when #{language} = 'en-US' then t.en_us
                   else m.model_name
                   end as model_name,
               m.identifier, m.is_share_perm, m.remark
        from iot_things_model m
                 left join iot_things_model_translate t on m.model_id = t.id
        where m.product_id=#{productId} and is_share_perm=1
        order by model_order desc,create_time desc
    </select>


    <select id="selectThingsModelListByProductIds" parameterType="String" resultMap="ThingsModelResult">
        select m.model_id,
        case
        when #{language} = 'zh-CN' then m.model_name
        when #{language} = 'en-US' then t.en_us
        else m.model_name
        end as model_name,
        m.product_id, m.product_name, m.tenant_id, m.tenant_name, m.identifier, m.type, m.datatype, m.formula,
        m.specs, m.is_chart, m.is_share_perm, m.is_history, m.is_monitor, m.is_app, m.del_flag, m.create_by,
        m.create_time, m.update_by, m.update_time, m.remark, m.is_readonly, m.model_order
        from iot_things_model m
        left join iot_things_model_translate t on m.model_id = t.id
        where is_chart=1 and m.product_id in
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>

    <select id="selectThingsModelByModelId" resultMap="ThingsModelResult">
        select m.model_id,
               case
                   when #{language} = 'zh-CN' then m.model_name
                   when #{language} = 'en-US' then t.en_us
                   else m.model_name
                   end as model_name,
               m.product_id, m.product_name, m.tenant_id, m.tenant_name, m.identifier, m.type, m.datatype, m.formula,
               m.specs, m.is_chart, m.is_share_perm, m.is_history, m.is_monitor, m.is_app, m.del_flag, m.create_by,
               m.create_time, m.update_by, m.update_time, m.remark, m.is_readonly, m.model_order
        from iot_things_model m
                 left join iot_things_model_translate t on m.model_id = t.id
        where model_id = #{modelId}
    </select>

    <select id="selectSingleThingsModel" parameterType="com.ssac.iot.domain.ThingsModel" resultMap="ThingsModelResult">
        select m.model_id,
        case
        when #{language} = 'zh-CN' then m.model_name
        when #{language} = 'en-US' then t.en_us
        else m.model_name
        end as model_name,
        m.product_id, m.product_name, m.tenant_id, m.tenant_name, m.identifier, m.type, m.datatype, m.formula,
        m.specs, m.is_chart, m.is_share_perm, m.is_history, m.is_monitor, m.is_app, m.del_flag, m.create_by,
        m.create_time, m.update_by, m.update_time, m.remark, m.is_readonly, m.model_order
        from iot_things_model m
        left join iot_things_model_translate t on m.model_id = t.id
        <where>
            <if test="productId != null">
                and m.product_id = #{productId}
            </if>
            <if test="identifier !=null and identifier != ''">
                and identifier = #{identifier}
            </if>
        </where>
    </select>

    <insert id="insertThingsModel" parameterType="com.ssac.iot.domain.ThingsModel" useGeneratedKeys="true"
            keyProperty="modelId">
        insert into iot_things_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="modelName != null and modelName != ''">
                model_name,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="productName != null and productName != ''">
                product_name,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="tenantName != null and tenantName != ''">
                tenant_name,
            </if>
            <if test="identifier != null and identifier != ''">
                identifier,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="datatype != null and datatype != ''">
                datatype,
            </if>
            <if test="formula != null">
                formula,
            </if>
            <if test="specs != null and specs != ''">
                specs,
            </if>
            <if test="isChart != null">
                is_chart,
            </if>
            <if test="isSharePerm != null">
                is_share_perm,
            </if>
            <if test="isHistory != null">
                is_history,
            </if>
            <if test="isApp != null">
                is_app,
            </if>
            <if test="isMonitor != null">
                is_monitor,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="isReadonly != null">
                is_readonly,
            </if>
            <if test="modelOrder != null">
                model_order,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="modelName != null and modelName != ''">
                #{modelName},
            </if>
            <if test="productId != null">
                #{productId},
            </if>
            <if test="productName != null and productName != ''">
                #{productName},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="tenantName != null and tenantName != ''">
                #{tenantName},
            </if>
            <if test="identifier != null and identifier != ''">
                #{identifier},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="datatype != null and datatype != ''">
                #{datatype},
            </if>
            <if test="formula != null">
                #{formula},
            </if>
            <if test="specs != null and specs != ''">
                #{specs},
            </if>
            <if test="isChart != null">
                #{isChart},
            </if>
            <if test="isSharePerm != null">
                #{isSharePerm},
            </if>
            <if test="isHistory != null">
                #{isHistory},
            </if>
            <if test="isMonitor != null">
                #{isMonitor},
            </if>
            <if test="isApp != null">
                #{isApp},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="isReadonly != null">
                #{isReadonly},
            </if>
            <if test="modelOrder != null">
                #{modelOrder},
            </if>
        </trim>
    </insert>

    <insert id="insertBatchThingsModel" parameterType="com.ssac.iot.domain.ThingsModel" useGeneratedKeys="true"
            keyProperty="modelId">
        INSERT INTO iot_things_model (
        model_name,
        product_id,
        product_name,
        tenant_id,
        tenant_name,
        identifier,
        type,
        datatype,
        specs,
        is_chart,
        is_share_perm,
        is_history,
        is_monitor,
        is_app,
        is_readonly,
        create_by,
        create_time,
        formula
        )
        VALUES
        <foreach item="model" collection="list" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{model.modelName},#{model.productId},#{model.productName},#{model.tenantId},
                #{model.tenantName},#{model.identifier},#{model.type},#{model.datatype},
                #{model.specs},#{model.isChart},#{model.isSharePerm},#{model.isHistory},
                #{model.isMonitor},#{model.isApp},#{model.isReadonly},#{model.createBy},#{model.createTime},
                #{model.formula}
            </trim>
        </foreach>
    </insert>

    <update id="updateThingsModel" parameterType="com.ssac.iot.domain.ThingsModel">
        update iot_things_model
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelName != null and modelName != ''">
                model_name = #{modelName},
            </if>
            <if test="productId != null">
                product_id = #{productId},
            </if>
            <if test="productName != null and productName != ''">
                product_name = #{productName},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
            <if test="tenantName != null and tenantName != ''">
                tenant_name = #{tenantName},
            </if>
            <if test="identifier != null and identifier != ''">
                identifier = #{identifier},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="datatype != null and datatype != ''">
                datatype = #{datatype},
            </if>
            <if test="formula != null">
                formula = #{formula},
            </if>
            <if test="specs != null and specs != ''">
                specs = #{specs},
            </if>
            <if test="isChart != null">
                is_chart = #{isChart},
            </if>
            <if test="isSharePerm != null">
                is_share_perm = #{isSharePerm},
            </if>
            <if test="isHistory != null">
                is_history = #{isHistory},
            </if>
            <if test="isMonitor != null">
                is_monitor = #{isMonitor},
            </if>
            <if test="isApp != null">
                is_app = #{isApp},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="isReadonly != null">
                is_readonly = #{isReadonly},
            </if>
            <if test="modelOrder != null">
                model_order = #{modelOrder},
            </if>
        </trim>
        where model_id = #{modelId}
    </update>

    <delete id="deleteThingsModelByModelId" parameterType="Long">
        delete
        from iot_things_model
        where model_id = #{modelId}
    </delete>

    <delete id="deleteThingsModelByModelIds" parameterType="String">
        delete from iot_things_model where model_id in
        <foreach item="modelId" collection="array" open="(" separator="," close=")">
            #{modelId}
        </foreach>
    </delete>

    <select id="selectThingsModelIsMonitor" parameterType="com.ssac.iot.model.ThingsModels.ThingsItems"
            resultType="com.ssac.common.core.iot.response.IdentityAndName">
        select m.is_monitor as isMonitor,
        m.is_chart as isChart,
        m.is_share_perm as isSharePerm,
        m.is_history as isHistory,
        m.specs as dataType,
        case
        when #{language} = 'zh-CN' then m.model_name
        when #{language} = 'en-US' then t.en_us
        else m.model_name
        end as name,
        m.type as type,
        m.formula as formula,
        from iot_things_model m
        left join iot_things_model_translate t on m.model_id = t.id
        where m.product_id = #{productId,jdbcType=BIGINT}
        and m.identifier in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <delete id="deleteThingsModelByProductId" parameterType="long">
        delete from iot_things_model where product_id = #{productId}
    </delete>

    <select id="listSimByProductIds" resultType="com.ssac.iot.model.ThingsModelSimVO">
        select m.product_id, m.identifier,
        case
        when #{language} = 'zh-CN' then m.model_name
        when #{language} = 'en-US' then t.en_us
        else m.model_name
        end as model_name,
        m.type as thingsModelType,
        m.datatype
        from iot_things_model m
        left join iot_things_model_translate t on m.model_id = t.id
        where m.product_id in
        <foreach collection="productIdList" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>

    <select id="getSpecsByProductIdAndIdentifier" resultType="java.lang.String">
        select specs
        from iot_things_model
        where product_id = #{productId}
          and identifier = #{identifier}
    </select>

    <select id="listSceneModelDataByModelIds" resultType="com.ssac.iot.domain.ThingsModel">
        select m.model_id, m.identifier,
        case
        when #{language} = 'zh-CN' then m.model_name
        when #{language} = 'en-US' then t.en_us
        else m.model_name
        end as model_name,
        from iot_things_model m
        left join iot_things_model_translate t on m.model_id = t.id
        where m.model_id in
        <foreach collection="modelIdList" item="modelId" open="(" separator="," close=")">
            #{modelId}
        </foreach>
    </select>


    <select id="getModbusConfigUnSelectThingsModel" resultMap="ThingsModelAndModbusResult">
        select m.model_id, m.identifier,
               case
                   when #{language} = 'zh-CN' then m.model_name
                   when #{language} = 'en-US' then t.en_us
                   else m.model_name
                   end as model_name,
               m.datatype,
               case
                   when (select count(1) from iot_modbus_config c where c.product_id = #{productId} and c.identifier = m.identifier) > 0 then 0
                   else 1
                   end as is_select
        from iot_things_model m
        left join iot_things_model_translate t on m.model_id = t.id
        where m.product_id = #{productId}
    </select>

    <select id="selectThingsModelListByModelIds" resultType="com.ssac.iot.domain.ThingsModel">
        select model_id, type
        from iot_things_model where model_id in
        <foreach item="modelId" collection="modelIdList" open="(" separator="," close=")">
            #{modelId}
        </foreach>
        and del_flag = 0
    </select>

    <select id="selectThingsModelBySerialNumber" resultMap="ThingsModelResult">
        select m.model_id, m.identifier,
               case
                   when #{language} = 'zh-CN' then m.model_name
                   when #{language} = 'en-US' then t.en_us
                   else m.model_name
                   end as model_name
        from iot_things_model m
        inner join iot_device d on m.product_id = d.product_id
        left join iot_things_model_translate t on m.model_id = t.id
        where d.device_id = #{deviceId}
        and m.is_readonly = 0
    </select>
</mapper>

