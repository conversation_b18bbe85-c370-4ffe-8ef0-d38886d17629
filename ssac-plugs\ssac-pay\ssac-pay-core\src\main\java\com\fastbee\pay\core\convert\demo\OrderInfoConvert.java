package com.ssac.pay.core.convert.demo;

import com.ssac.common.core.domain.PageResult;
import com.ssac.pay.core.controller.admin.demo.vo.OrderInfoCreateReqVO;
import com.ssac.pay.core.controller.admin.demo.vo.OrderInfoRespVO;
import com.ssac.pay.core.domain.dataobject.demo.OrderInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 示例订单 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface OrderInfoConvert {

    OrderInfoConvert INSTANCE = Mappers.getMapper(OrderInfoConvert.class);

    OrderInfo convert(OrderInfoCreateReqVO bean);

    OrderInfoRespVO convert(OrderInfo bean);

    PageResult<OrderInfoRespVO> convertPage(PageResult<OrderInfo> page);

}
