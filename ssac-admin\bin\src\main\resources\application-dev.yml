# 数据源配�?spring:
  datasource:
    dynamic:
      druid:
        initial-size: 5
        min-idle: 10
        max-wait: 60000
        max-active: 20
        timeBetweenEvictionRunsMillis: 60000  # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        minEvictableIdleTimeMillis: 300000    # 配置一个连接在池中最小生存的时间，单位是毫秒
        maxEvictableIdleTimeMillis: 900000    # 配置一个连接在池中最大生存的时间，单位是毫秒
        validation-query: 'SELECT 1'
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
      datasource:
        master:
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************************************************
          username: root
          password: NFZX505050
          druid:
            filters: stat,wall
            stat:
              # 慢SQL记录
              log-slow-sql: true
              slow-sql-millis: 1000
              merge-sql: true
            wall:
              none-base-statement-allow: true
        taos: # 配置 taos 数据�?          enabled: true
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.taosdata.jdbc.TSDBDriver
          url: *****************************************************************
          username: root
          password: taosdata
          dbName: fastbee_log
#        slave:
#          type: com.alibaba.druid.pool.DruidDataSource
#          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: *************************************************************************************************************************************************
#          username: root
#          password: fastbee
  shardingsphere:
#    props:
      # 是否显示 ShardingSpher 的sql，用于Debug
#      sql-show: true
    datasource:
      # 配置真实数据�?      names: ds0
      ds0: # 配置 mysql 数据�?        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ***********************************************************************************************************************************************
        username: root
        password: NFZX505050
        filters: stat,wall
        filter:
          stat:
            enabled: true
            # 慢SQL记录
            log-slow-sql: true
            slow-sql-millis: 1000
            merge-sql: true
          wall:
            config:
              multi-statement-allow: true

    rules: # 配置表规�?      sharding:
        # 表策略配�?        tables:
          # iot_device_log 是逻辑�?          iot_device_log:
            actualDataNodes: ds0.iot_device_log_$->{2024..2030}0$->{1..9},ds0.iot_device_log_$->{2024..2030}1$->{0..2}
            tableStrategy:
              # 使用标准分片策略
              standard:
                # 配置分片字段
                shardingColumn: create_time
                # 分片算法名称，不支持大写字母和下划线，否则启动就会报�?                shardingAlgorithmName: time-sharding-algorithm
        # 分片算法配置
        shardingAlgorithms:
          # 分片算法名称，不支持大写字母和下划线，否则启动就会报�?          time-sharding-algorithm:
            # 类型：自定义策略
            type: CLASS_BASED
            props:
              # 分片策略
              strategy: standard
              # 分片算法�?              algorithmClassName: com.ssac.framework.config.sharding.TimeShardingAlgorithm


  # redis 配置
  redis:
    host: http://*************                        # 地址
    port: 6379                              # 端口，默认为6379
    database: 3                             # 数据库索�?    password:'cceste660'                       # 密码
    timeout: 10s                            # 连接超时时间
    lettuce:
      pool:
        min-idle: 0                         # 连接池中的最小空闲连�?        max-idle: 8                         # 连接池中的最大空闲连�?        max-active: 8                       # 连接池的最大数据库连接�?        max-wait: -1ms                      # 连接池最大阻塞等待时间（使用负值表示没有限制）
  # mqtt 配置
  mqtt:
    username: admin                       # 账号
    password: NFZX@2024                      # 密码
    host-url: tcp://*************:1883         # mqtt连接tcp地址
    client-id: 1111111                # 客户端Id，不能相同，采用随机�?${random.value}
    default-topic: test                     # 默认主题
    timeout: 30                             # 超时时间
    keepalive: 30                           # 保持连接
    clearSession: true                      # 清除会话(设置为false,断开连接，重连后使用原来的会�?保留订阅的主题，能接收离线期间的消息)

# sip 配置
sip:
  enabled: false                            # 是否启用视频监控SIP，true为启�?  ## 本地调试时，绑定网卡局域网IP，设备在同一局域网，设备接入IP填写绑定IP
  ## 部署服务端时，默认绑定容器IP，设备接入IP填写服务器公网IP
  ip: **********
  port: 5061                                # SIP端口(保持默认)
  domain: 3402000000                        # 由省级、市级、区级、基层编号组�?  id: 34020000002000000001                  # 同上，另外增加编号，(可保持默�?
  password: 12345678                        # 监控设备接入的密�?  log: true
  zlmRecordPath: /opt/media/bin/www
  mp4_max_second: 30                        # 视频录像时长，单位秒

# 日志配置
logging:
  level:
    com.ssac: debug
    com.yomahub: debug
    org.dromara: warn
    org.springframework: warn
    com.baomidou: debug

# Swagger配置
swagger:
  enabled: true                             # 是否开启swagger
  pathMapping: /dev-api                     # 请求前缀


