<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssac.oss.mapper.OssDetailMapper">
    
    <resultMap type="com.ssac.oss.domain.OssDetail" id="OssDetailResult">
        <result property="id"    column="id"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="tenantName"    column="tenant_name"    />
        <result property="fileName"    column="file_name"    />
        <result property="originalName"    column="original_name"    />
        <result property="fileSuffix"    column="file_suffix"    />
        <result property="url"    column="url"    />
        <result property="service"    column="service"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOssDetailVo">
        select id, tenant_id, tenant_name, file_name, original_name, file_suffix, url, service, del_flag, create_by, create_time, update_by, update_time, remark from oss_detail
    </sql>

    <select id="selectOssDetailList" parameterType="com.ssac.oss.domain.OssDetail" resultMap="OssDetailResult">
        <include refid="selectOssDetailVo"/>
        <where>  
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
            <if test="tenantName != null  and tenantName != ''"> and tenant_name like concat('%', #{tenantName}, '%')</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="originalName != null  and originalName != ''"> and original_name like concat('%', #{originalName}, '%')</if>
            <if test="fileSuffix != null  and fileSuffix != ''"> and file_suffix = #{fileSuffix}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="service != null  and service != ''"> and service = #{service}</if>
        </where>
    </select>
    
    <select id="selectOssDetailById" parameterType="Integer" resultMap="OssDetailResult">
        <include refid="selectOssDetailVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOssDetail" parameterType="com.ssac.oss.domain.OssDetail" useGeneratedKeys="true" keyProperty="id">
        insert into oss_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">tenant_id,</if>
            <if test="tenantName != null and tenantName != ''">tenant_name,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="originalName != null and originalName != ''">original_name,</if>
            <if test="fileSuffix != null and fileSuffix != ''">file_suffix,</if>
            <if test="url != null and url != ''">url,</if>
            <if test="service != null and service != ''">service,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">#{tenantId},</if>
            <if test="tenantName != null and tenantName != ''">#{tenantName},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="originalName != null and originalName != ''">#{originalName},</if>
            <if test="fileSuffix != null and fileSuffix != ''">#{fileSuffix},</if>
            <if test="url != null and url != ''">#{url},</if>
            <if test="service != null and service != ''">#{service},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOssDetail" parameterType="com.ssac.oss.domain.OssDetail">
        update oss_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="tenantName != null and tenantName != ''">tenant_name = #{tenantName},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="originalName != null and originalName != ''">original_name = #{originalName},</if>
            <if test="fileSuffix != null and fileSuffix != ''">file_suffix = #{fileSuffix},</if>
            <if test="url != null and url != ''">url = #{url},</if>
            <if test="service != null and service != ''">service = #{service},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOssDetailById" parameterType="Integer">
        delete from oss_detail where id = #{id}
    </delete>

    <delete id="deleteOssDetailByIds" parameterType="String">
        delete from oss_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
