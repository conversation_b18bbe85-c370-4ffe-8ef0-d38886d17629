package com.ssac.sip.server;

import com.ssac.sip.domain.SipDevice;

/**
 * <AUTHOR>
 */
public interface IRtspCmd {
    void playPause(SipDevice device, String channelId, String streamId);
    void playReplay(SipDevice device, String channelId, String streamId);
    void playBackSeek(SipDevice device, String channelId, String streamId, long seektime);
    void playBackSpeed(SipDevice device, String channelId, String streamId, Integer speed);
    void setCseq(String streamId);
}

