package com.ssac.base.core;

import com.ssac.base.core.annotation.Node;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class SpringHandlerMapping extends Abstract<PERSON>andlerMapping implements ApplicationContextAware {

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, Object> endpoints = applicationContext.getBeansWithAnnotation(Node.class);
        for (Object bean : endpoints.values()) {
            super.registerHandlers(bean);
        }
    }
}

