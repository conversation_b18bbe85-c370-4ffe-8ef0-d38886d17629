package ${packageName}.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.utils.poi.ExcelUtil;
import ${packageName}.domain.${ClassName};
import ${packageName}.model.${ClassName}VO;
import ${packageName}.service.I${ClassName}Service;
#if($table.crud || $table.sub)
import com.fastbee.common.core.page.TableDataInfo;
#elseif($table.tree)
#end

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RestController
@RequestMapping("/${moduleName}/${businessName}")
@Api(tags = "${functionName}")
public class ${ClassName}Controller extends BaseController {
    @Autowired
    private I${ClassName}Service ${className}Service;

    /**
     * 查询${functionName}列表
     */
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:list')")
    @GetMapping("/list")
    @ApiOperation("查询${functionName}列表")
    #if($table.crud || $table.sub)
    public TableDataInfo list(${ClassName} ${className}) {
        startPage();
        List<${ClassName}VO> list = ${className}Service.select${ClassName}VOList(${className});
        return getDataTable(list);
    }
    #elseif($table.tree)
        public AjaxResult list(${ClassName} ${className}) {
            List<${ClassName}VO> list = ${className}Service.select${ClassName}VOList(${className});
            return success(list);
        }
    #end

    /**
     * 导出${functionName}列表
     */
    @ApiOperation("导出${functionName}列表")
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ${ClassName} ${className}) {
        List<${ClassName}VO> list = ${className}Service.select${ClassName}VOList(${className});
        ExcelUtil<${ClassName}VO> util = new ExcelUtil<${ClassName}VO>(${ClassName}VO.class);
        util.exportExcel(response, list, "${functionName}数据");
    }

    /**
     * 获取${functionName}详细信息
     */
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:query')")
    @GetMapping(value = "/{${pkColumn.javaField}}")
    @ApiOperation("获取${functionName}详细信息")
    public AjaxResult getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        return success(${className}Service.queryByIdWithCache(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:add')")
    @PostMapping
    @ApiOperation("新增${functionName}")
    public AjaxResult add(@RequestBody ${ClassName} ${className}) {
        return toAjax(${className}Service.insertWithCache(${className}));
    }

    /**
     * 修改${functionName}
     */
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:edit')")
    @PutMapping
    @ApiOperation("修改${functionName}")
    public AjaxResult edit(@RequestBody ${ClassName} ${className}) {
        return toAjax(${className}Service.updateWithCache(${className}));
    }

    /**
     * 删除${functionName}
     */
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:remove')")
    @DeleteMapping("/{${pkColumn.javaField}s}")
    @ApiOperation("删除${functionName}")
    public AjaxResult remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        return toAjax(${className}Service.deleteWithCacheByIds(${pkColumn.javaField}s, true));
    }
}
